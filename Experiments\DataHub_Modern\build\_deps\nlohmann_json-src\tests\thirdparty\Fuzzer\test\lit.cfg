import lit.formats
import sys

config.name = "LLVMFuzzer"
config.test_format = lit.formats.ShTest(True)
config.suffixes = ['.test']
config.test_source_root = os.path.dirname(__file__)

# Tweak PATH to include llvm tools dir and current exec dir.
llvm_tools_dir = getattr(config, 'llvm_tools_dir', None)
if (not llvm_tools_dir) or (not os.path.exists(llvm_tools_dir)):
  lit_config.fatal("Invalid llvm_tools_dir config attribute: %r" % llvm_tools_dir)
path = os.path.pathsep.join((llvm_tools_dir, config.test_exec_root,
                             config.environment['PATH']))
config.environment['PATH'] = path

if config.has_lsan:
  lit_config.note('lsan feature available')
  config.available_features.add('lsan')
else:
  lit_config.note('lsan feature unavailable')

if sys.platform.startswith('linux'):
  # Note the value of ``sys.platform`` is not consistent
  # between python 2 and 3, hence the use of ``.startswith()``.
  lit_config.note('linux feature available')
  config.available_features.add('linux')
else:
  lit_config.note('linux feature unavailable')

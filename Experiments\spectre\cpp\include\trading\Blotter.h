#pragma once

/**
 * @file Blotter.h
 * @brief Order Management System for trading
 * <AUTHOR> from Python implementation by <PERSON><PERSON><PERSON><PERSON> (<PERSON>)
 * @copyright Copyright 2019-2020, Heerozh. All rights reserved.
 * @license Apache 2.0
 */

#include "Portfolio.h"
#include "Event.h"
#include <torch/torch.h>
#include <unordered_map>
#include <vector>
#include <functional>
#include <chrono>
#include <memory>

namespace Spectre {
namespace Trading {

// Forward declarations
class DataLoader;

/**
 * @brief Commission calculation model
 */
class CommissionModel {
public:
    /**
     * @brief Constructor
     * @param percentage Percentage commission (e.g., 0.0005 for 0.05%)
     * @param per_share Per-share commission
     * @param minimum Minimum commission
     */
    CommissionModel(double percentage = 0.0, double per_share = 0.0, double minimum = 0.0)
        : percentage_(percentage), per_share_(per_share), minimum_(minimum) {}

    /**
     * @brief Check if commission model is empty (no fees)
     * @return True if all fees are zero
     */
    bool is_empty() const {
        return (percentage_ + per_share_ + minimum_) == 0.0;
    }

    /**
     * @brief Calculate commission for a trade
     * @param asset Asset symbol
     * @param price Trade price
     * @param shares Number of shares
     * @return Commission amount
     */
    virtual double calculate(const std::string& asset, double price, int64_t shares) const {
        double commission = price * std::abs(shares) * percentage_;
        commission += std::abs(shares) * per_share_;
        return std::max(commission, minimum_);
    }

protected:
    double percentage_;
    double per_share_;
    double minimum_;
};

/**
 * @brief Slippage calculation model
 */
class SlippageModel : public CommissionModel {
public:
    /**
     * @brief Constructor
     * @param max_percentage Maximum slippage percentage
     * @param max_amount_to_volume_ratio Maximum amount to volume ratio
     */
    SlippageModel(double max_percentage = 0.0, double max_amount_to_volume_ratio = 2e-4)
        : CommissionModel(max_percentage, 0.0, 0.0), max_ratio_(max_amount_to_volume_ratio) {}

    /**
     * @brief Calculate slippage-adjusted price
     * @param asset Asset symbol
     * @param price Original price
     * @param amount_to_volume_ratio Ratio of trade amount to volume
     * @return Adjusted price with slippage
     */
    double calculate_price(const std::string& asset, double price, double amount_to_volume_ratio) const {
        if (max_ratio_ == 0.0) {
            return price;
        }

        // Sigmoid function for smooth interpolation
        auto sigmoid = [](double x, double edge0, double edge1) {
            return 1.0 / (1.0 + std::pow(200.0, -((x - edge0) / (edge1 - edge0)) + 0.5));
        };

        double slippage = price * percentage_ * sigmoid(amount_to_volume_ratio, 0.0, max_ratio_);
        return price + std::max(slippage, minimum_);
    }

private:
    double max_ratio_;
};

/**
 * @brief Daily trading curb model
 */
class DailyCurbModel {
public:
    /**
     * @brief Constructor
     * @param percentage Maximum daily price change percentage
     */
    explicit DailyCurbModel(double percentage) : percentage_(percentage) {}

    /**
     * @brief Check if trade is allowed based on daily price limits
     * @param asset Asset symbol
     * @param current_price Current price
     * @param shares Number of shares to trade
     * @param current_high Current bar high price
     * @param current_low Current bar low price
     * @param last_close Previous day close price
     * @param last_div Previous day dividend
     * @param last_sp Previous day split ratio
     * @return Pair of (adjusted_price, allowed_shares)
     */
    std::pair<double, int64_t> calculate(
        const std::string& asset, double current_price, int64_t shares,
        double current_high, double current_low,
        double last_close, double last_div, double last_sp) const {
        
        double adjusted_close = std::round((last_close - last_div) * last_sp * 100.0) / 100.0;
        
        if (std::abs(current_price / adjusted_close - 1.0) >= percentage_) {
            return {current_price, 0}; // Trading halted
        } else {
            return {current_price, shares}; // Trading allowed
        }
    }

private:
    double percentage_;
};

/**
 * @brief Order structure
 */
struct Order {
    using TimePoint = std::chrono::system_clock::time_point;
    
    TimePoint date;
    std::string asset;
    int64_t amount;
    double price;
    double fill_price;
    double commission;
    double realized;

    Order(const TimePoint& dt, const std::string& symbol, int64_t amt, 
          double p, double fp, double comm, double real)
        : date(dt), asset(symbol), amount(amt), price(p), 
          fill_price(fp), commission(comm), realized(real) {}
};

/**
 * @brief Base class for Order Management System
 */
class BaseBlotter {
public:
    using TimePoint = std::chrono::system_clock::time_point;
    using AssetId = std::string;
    using PriceFunction = std::function<double(const AssetId&)>;

    static constexpr int64_t MAX_SHARES = 100000;

    /**
     * @brief Constructor
     */
    BaseBlotter();

    /**
     * @brief Destructor
     */
    virtual ~BaseBlotter() = default;

    // Getters
    const Portfolio& portfolio() const { return portfolio_; }
    Portfolio& portfolio() { return portfolio_; }
    const std::unordered_map<AssetId, Position>& positions() const { return portfolio_.positions(); }

    /**
     * @brief Set current datetime
     * @param dt New datetime
     */
    virtual void set_datetime(const TimePoint& dt);

    /**
     * @brief Set commission model
     * @param percentage Percentage commission
     * @param per_share Per-share commission
     * @param minimum Minimum commission
     */
    void set_commission(double percentage, double per_share, double minimum);

    /**
     * @brief Set slippage model
     * @param max_percentage Maximum slippage percentage
     * @param max_volume_ratio Maximum volume ratio
     */
    void set_slippage(double max_percentage, double max_volume_ratio);

    /**
     * @brief Set short selling fee
     * @param percentage Short fee percentage
     */
    void set_short_fee(double percentage);

    /**
     * @brief Get current price for asset(s)
     * @param asset Asset symbol or collection of symbols
     * @return Price or price map
     */
    virtual double get_price(const AssetId& asset) = 0;
    virtual std::unordered_map<AssetId, double> get_prices(const std::vector<AssetId>& assets);

    /**
     * @brief Place an order
     * @param asset Asset symbol
     * @param amount Number of shares (positive for buy, negative for sell)
     * @param price Order price (nullptr for market order)
     * @return True if order was successfully placed
     */
    virtual bool order(const AssetId& asset, int64_t amount, double* price = nullptr);

    /**
     * @brief Order to target position
     * @param asset Asset symbol
     * @param target Target number of shares
     * @return True if order was successfully placed
     */
    virtual bool order_target(const AssetId& asset, int64_t target);

    /**
     * @brief Order to target percentage of portfolio
     * @param asset Asset symbol
     * @param pct Target percentage (0.0 to 1.0)
     * @return True if order was successfully placed
     */
    virtual bool order_target_percent(const AssetId& asset, double pct);

    /**
     * @brief Batch order to target positions
     * @param assets Vector of asset symbols
     * @param targets Vector of target shares
     * @return Vector of skipped orders
     */
    virtual std::vector<std::pair<AssetId, int64_t>> batch_order_target(
        const std::vector<AssetId>& assets, const std::vector<int64_t>& targets);

    /**
     * @brief Batch order to target percentages
     * @param assets Vector of asset symbols
     * @param weights Vector of target weights
     * @return Vector of skipped orders
     */
    virtual std::vector<std::pair<AssetId, int64_t>> batch_order_target_percent(
        const std::vector<AssetId>& assets, const std::vector<double>& weights);

    /**
     * @brief Cancel all pending orders
     */
    virtual void cancel_all_orders() = 0;

    /**
     * @brief Get historical positions
     * @return Portfolio history
     */
    const std::vector<Portfolio::HistoryRecord>& get_historical_positions() const;

    /**
     * @brief Get portfolio returns
     * @return Returns tensor
     */
    torch::Tensor get_returns() const;

    /**
     * @brief Get transaction history
     * @return Vector of orders
     */
    virtual std::vector<Order> get_transactions() const = 0;

protected:
    Portfolio portfolio_;
    TimePoint current_dt_;
    CommissionModel commission_;
    SlippageModel slippage_;
    CommissionModel short_fee_;
    CommissionModel div_tax_;
    bool long_only_;
    int64_t order_multiplier_;
    double borrow_money_interest_rate_;
    double borrow_stock_interest_rate_;

    /**
     * @brief Internal order implementation
     * @param asset Asset symbol
     * @param amount Number of shares
     * @param price Order price
     * @return True if order was successfully placed
     */
    virtual bool _order(const AssetId& asset, int64_t amount, double* price) = 0;

    /**
     * @brief Internal order target implementation
     * @param asset Asset symbol
     * @param target Target shares
     * @return True if order was successfully placed
     */
    virtual bool _order_target(const AssetId& asset, int64_t target);
};

} // namespace Trading
} // namespace Spectre

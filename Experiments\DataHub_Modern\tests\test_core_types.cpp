#include <catch2/catch_test_macros.hpp>
#include "core/Types.h"
#include "core/MarketData.h"

using namespace DataHub::Core;

TEST_CASE("Types - Enum conversions", "[types]") {
    SECTION("MarketType conversions") {
        REQUIRE(to_string(MarketType::Stock) == "Stock");
        REQUIRE(to_string(MarketType::Future) == "Future");
        REQUIRE(to_string(MarketType::Option) == "Option");
        
        REQUIRE(parse_market_type("Stock") == MarketType::Stock);
        REQUIRE(parse_market_type("Future") == MarketType::Future);
        REQUIRE(parse_market_type("Invalid") == std::nullopt);
    }
    
    SECTION("Exchange conversions") {
        REQUIRE(to_string(Exchange::SSE) == "SSE");
        REQUIRE(to_string(Exchange::SZSE) == "SZSE");
        REQUIRE(to_string(Exchange::SHFE) == "SHFE");
        
        REQUIRE(parse_exchange("SSE") == Exchange::SSE);
        REQUIRE(parse_exchange("SZSE") == Exchange::SZSE);
        REQUIRE(parse_exchange("Invalid") == std::nullopt);
    }
    
    SECTION("BarSize conversions") {
        REQUIRE(to_string(BarSize::Minute1) == "1m");
        REQUIRE(to_string(BarSize::Minute5) == "5m");
        REQUIRE(to_string(BarSize::Day) == "1d");
        
        REQUIRE(parse_bar_size("1m") == BarSize::Minute1);
        REQUIRE(parse_bar_size("5m") == BarSize::Minute5);
        REQUIRE(parse_bar_size("Invalid") == std::nullopt);
    }
}

TEST_CASE("Types - Result class", "[types]") {
    SECTION("Success result") {
        auto result = make_success(42);
        REQUIRE(result.is_success());
        REQUIRE_FALSE(result.is_error());
        REQUIRE(result.value() == 42);
    }
    
    SECTION("Error result") {
        auto result = make_error<int>(ErrorCode::InvalidParameter, "Test error");
        REQUIRE_FALSE(result.is_success());
        REQUIRE(result.is_error());
        REQUIRE(result.error() == ErrorCode::InvalidParameter);
        REQUIRE(result.error_message() == "Test error");
    }
    
    SECTION("Void result") {
        auto success = make_success();
        REQUIRE(success.is_success());
        
        auto error = make_error(ErrorCode::DatabaseError, "DB error");
        REQUIRE(error.is_error());
        REQUIRE(error.error() == ErrorCode::DatabaseError);
    }
}

TEST_CASE("Types - Utility functions", "[types]") {
    SECTION("Numeric comparisons") {
        REQUIRE(is_equal(1.0, 1.0));
        REQUIRE(is_equal(1.0, 1.0000001, 1e-6));
        REQUIRE_FALSE(is_equal(1.0, 1.1));
        
        REQUIRE(is_zero(0.0));
        REQUIRE(is_zero(1e-10));
        REQUIRE_FALSE(is_zero(0.1));
        
        REQUIRE(is_positive(1.0));
        REQUIRE_FALSE(is_positive(0.0));
        REQUIRE_FALSE(is_positive(-1.0));
        
        REQUIRE(is_negative(-1.0));
        REQUIRE_FALSE(is_negative(0.0));
        REQUIRE_FALSE(is_negative(1.0));
    }
    
    SECTION("String utilities") {
        REQUIRE(trim("  hello  ") == "hello");
        REQUIRE(trim("") == "");
        REQUIRE(trim("   ") == "");
        
        auto parts = split("a,b,c", ',');
        REQUIRE(parts.size() == 3);
        REQUIRE(parts[0] == "a");
        REQUIRE(parts[1] == "b");
        REQUIRE(parts[2] == "c");
        
        REQUIRE(join({"a", "b", "c"}, ",") == "a,b,c");
        REQUIRE(join({}, ",") == "");
    }
}

TEST_CASE("MarketData - QuoteData", "[marketdata]") {
    SECTION("Basic construction") {
        QuoteData quote("000001.SZ", now());
        quote.last_price = 10.50;
        quote.pre_close = 10.00;
        quote.volume = 1000;
        quote.amount = 10500.0;
        
        REQUIRE(quote.symbol == "000001.SZ");
        REQUIRE(quote.last_price == 10.50);
        REQUIRE(quote.is_valid());
    }
    
    SECTION("Price calculations") {
        QuoteData quote("000001.SZ", now());
        quote.last_price = 11.00;
        quote.pre_close = 10.00;
        quote.bid_prices[0] = 10.99;
        quote.ask_prices[0] = 11.01;
        
        REQUIRE(is_equal(quote.change_percent(), 10.0));
        REQUIRE(is_equal(quote.change_amount(), 1.0));
        REQUIRE(is_equal(quote.mid_price(), 11.0));
        REQUIRE(is_equal(quote.spread(), 0.02));
    }
    
    SECTION("Validation") {
        QuoteData valid_quote("000001.SZ", now());
        valid_quote.last_price = 10.0;
        valid_quote.volume = 1000;
        valid_quote.amount = 10000.0;
        REQUIRE(valid_quote.is_valid());
        
        QuoteData invalid_quote;
        REQUIRE_FALSE(invalid_quote.is_valid());
        
        QuoteData negative_price("000001.SZ", now());
        negative_price.last_price = -1.0;
        REQUIRE_FALSE(negative_price.is_valid());
    }
}

TEST_CASE("MarketData - BarData", "[marketdata]") {
    SECTION("Basic construction") {
        BarData bar("000001.SZ", now(), BarSize::Minute5, BarType::Candle);
        bar.open = 10.0;
        bar.high = 10.5;
        bar.low = 9.8;
        bar.close = 10.2;
        bar.volume = 1000;
        bar.amount = 10100.0;
        
        REQUIRE(bar.symbol == "000001.SZ");
        REQUIRE(bar.bar_size == BarSize::Minute5);
        REQUIRE(bar.is_valid());
    }
    
    SECTION("Price calculations") {
        BarData bar("000001.SZ", now(), BarSize::Day, BarType::Candle);
        bar.open = 10.0;
        bar.high = 11.0;
        bar.low = 9.5;
        bar.close = 10.5;
        
        REQUIRE(is_equal(bar.typical_price(), 10.333333, 1e-5));
        REQUIRE(is_equal(bar.weighted_close(), 10.25));
        REQUIRE(is_equal(bar.price_range(), 1.5));
        REQUIRE(is_equal(bar.body_size(), 0.5));
        REQUIRE(is_equal(bar.upper_shadow(), 0.5));
        REQUIRE(is_equal(bar.lower_shadow(), 0.5));
        
        REQUIRE(bar.is_bullish());
        REQUIRE_FALSE(bar.is_bearish());
        REQUIRE_FALSE(bar.is_doji());
    }
    
    SECTION("Update from quote") {
        BarData bar("000001.SZ", now(), BarSize::Minute1, BarType::Candle);
        
        QuoteData quote1("000001.SZ", now());
        quote1.last_price = 10.0;
        quote1.volume = 100;
        quote1.amount = 1000.0;
        
        bar.update(quote1);
        REQUIRE(bar.open == 10.0);
        REQUIRE(bar.high == 10.0);
        REQUIRE(bar.low == 10.0);
        REQUIRE(bar.close == 10.0);
        
        QuoteData quote2("000001.SZ", now());
        quote2.last_price = 10.5;
        quote2.volume = 200;
        quote2.amount = 2100.0;
        
        bar.update(quote2);
        REQUIRE(bar.open == 10.0);
        REQUIRE(bar.high == 10.5);
        REQUIRE(bar.low == 10.0);
        REQUIRE(bar.close == 10.5);
        REQUIRE(bar.volume == 200);
        REQUIRE(bar.amount == 2100.0);
    }
}

TEST_CASE("MarketData - TickData", "[marketdata]") {
    SECTION("Basic construction") {
        TickData tick("000001.SZ", now(), 10.5, 100, 1050.0);
        
        REQUIRE(tick.symbol == "000001.SZ");
        REQUIRE(tick.price == 10.5);
        REQUIRE(tick.volume == 100);
        REQUIRE(tick.amount == 1050.0);
        REQUIRE(tick.is_valid());
    }
    
    SECTION("Direction") {
        TickData tick("000001.SZ", now(), 10.5, 100, 1050.0);
        tick.direction = TickData::Direction::Buy;
        
        REQUIRE(tick.direction == TickData::Direction::Buy);
    }
}

TEST_CASE("MarketData - Utility functions", "[marketdata]") {
    SECTION("Bar interval calculation") {
        REQUIRE(get_bar_interval(BarSize::Second1) == std::chrono::seconds{1});
        REQUIRE(get_bar_interval(BarSize::Minute1) == std::chrono::minutes{1});
        REQUIRE(get_bar_interval(BarSize::Minute5) == std::chrono::minutes{5});
        REQUIRE(get_bar_interval(BarSize::Hour1) == std::chrono::hours{1});
        REQUIRE(get_bar_interval(BarSize::Day) == std::chrono::hours{24});
    }
    
    SECTION("Timestamp alignment") {
        auto timestamp = Timestamp{std::chrono::milliseconds{1609459261500}}; // 2021-01-01 00:01:01.500
        auto interval = std::chrono::minutes{5};
        
        auto aligned = align_timestamp_to_interval(timestamp, interval);
        auto expected = Timestamp{std::chrono::milliseconds{1609459200000}}; // 2021-01-01 00:00:00.000
        
        REQUIRE(aligned == expected);
    }
}

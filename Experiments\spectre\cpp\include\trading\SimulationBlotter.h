#pragma once

/**
 * @file SimulationBlotter.h
 * @brief Simulation blotter for backtesting
 * <AUTHOR> from Python implementation by <PERSON><PERSON><PERSON><PERSON> (<PERSON>)
 * @copyright Copyright 2019-2020, Heerozh. All rights reserved.
 * @license Apache 2.0
 */

#include "Blotter.h"
#include "Event.h"
#include <unordered_map>
#include <vector>
#include <memory>

namespace Spectre {
namespace Trading {

// Forward declarations
class DataLoader;

/**
 * @brief Simulation blotter for backtesting
 */
class SimulationBlotter : public BaseBlotter, public EventReceiver {
public:
    using TimePoint = std::chrono::system_clock::time_point;
    using AssetId = std::string;

    /**
     * @brief Constructor
     * @param capital_base Initial capital
     * @param daily_curb Daily price limit model (optional)
     */
    explicit SimulationBlotter(double capital_base = 100000.0, 
                              std::shared_ptr<DailyCurbModel> daily_curb = nullptr);

    /**
     * @brief Destructor
     */
    ~SimulationBlotter() override = default;

    // Getters
    bool market_opened() const { return market_opened_; }
    double capital_base() const { return capital_base_; }

    /**
     * @brief Clear all orders and reset portfolio
     */
    void clear();

    /**
     * @brief Set current datetime
     * @param dt New datetime
     */
    void set_datetime(const TimePoint& dt) override;

    /**
     * @brief Set current price source (open or close)
     * @param name Price source name ("open" or "close")
     */
    void set_price(const std::string& name);

    /**
     * @brief Set price data for current datetime
     * @param prices Price map for all assets
     */
    void set_current_prices(const std::unordered_map<AssetId, double>& prices);

    /**
     * @brief Set volume data for current datetime
     * @param volumes Volume map for all assets
     */
    void set_current_volumes(const std::unordered_map<AssetId, double>& volumes);

    /**
     * @brief Set OHLC data for current datetime
     * @param ohlc_data OHLC data map (asset -> [open, high, low, close])
     */
    void set_current_ohlc(const std::unordered_map<AssetId, std::vector<double>>& ohlc_data);

    /**
     * @brief Set previous day data for daily curb calculations
     * @param prev_data Previous day data (asset -> [close, dividend, split_ratio])
     */
    void set_previous_day_data(const std::unordered_map<AssetId, std::vector<double>>& prev_data);

    /**
     * @brief Get current price for asset
     * @param asset Asset symbol
     * @return Current price
     */
    double get_price(const AssetId& asset) override;

    /**
     * @brief Cancel all pending orders
     */
    void cancel_all_orders() override;

    /**
     * @brief Get transaction history
     * @return Vector of orders
     */
    std::vector<Order> get_transactions() const override;

    /**
     * @brief Update portfolio values with current prices
     */
    void update_portfolio_value();

    /**
     * @brief Process dividend payments
     * @param asset Asset symbol
     * @param amount Dividend amount per share
     * @param tax Tax rate
     */
    void process_dividend(const AssetId& asset, double amount, double tax = 0.0);

    /**
     * @brief Process stock splits
     * @param asset Asset symbol
     * @param inverse_ratio Split ratio (e.g., 0.5 for 2:1 split)
     * @param last_price Price after split
     */
    void process_split(const AssetId& asset, double inverse_ratio, double last_price);

    // Event handlers
    void market_open(EventReceiver* source);
    void market_close(EventReceiver* source);
    void new_bars_data(EventReceiver* source);

    // EventReceiver interface
    void on_run() override;

protected:
    /**
     * @brief Internal order implementation
     * @param asset Asset symbol
     * @param amount Number of shares
     * @param price Order price (ignored for market orders)
     * @return True if order was successfully placed
     */
    bool _order(const AssetId& asset, int64_t amount, double* price) override;

private:
    bool market_opened_;
    double capital_base_;
    std::shared_ptr<DailyCurbModel> daily_curb_;
    
    // Current market data
    std::unordered_map<AssetId, double> current_prices_;
    std::unordered_map<AssetId, double> current_volumes_;
    std::unordered_map<AssetId, std::vector<double>> current_ohlc_; // [open, high, low, close]
    std::unordered_map<AssetId, std::vector<double>> previous_day_data_; // [close, div, split]
    
    // Order storage
    std::unordered_map<AssetId, std::vector<Order>> orders_;
    
    // Current price source
    enum class PriceSource { OPEN, CLOSE } current_price_source_;

    /**
     * @brief Get current prices map
     * @return Reference to current prices
     */
    const std::unordered_map<AssetId, double>& get_current_prices() const;

    /**
     * @brief Check if trading is allowed (market hours)
     * @throws std::runtime_error if market is closed
     */
    void check_market_hours() const;
};

} // namespace Trading
} // namespace Spectre

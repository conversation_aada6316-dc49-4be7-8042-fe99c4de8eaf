#!/bin/sh

# This file is part of CMake-codecov.
#
# Copyright (c)
#   2015-2017 RWTH Aachen University, Federal Republic of Germany
#
# See the LICENSE file in the package base directory for details
#
# Written by <PERSON>, <EMAIL>
#

if [ -z "$LLVM_COV_BIN" ]
then
	echo "LLVM_COV_BIN not set!" >& 2
	exit 1
fi


# Get LLVM version to find out.
LLVM_VERSION=$($LLVM_COV_BIN -version | grep -i "LLVM version" \
	| sed "s/^\([A-Za-z ]*\)\([0-9]\).\([0-9]\).*$/\2.\3/g")

if [ "$1" = "-v" ]
then
	echo "llvm-cov-wrapper $LLVM_VERSION"
	exit 0
fi


if [ -n "$LLVM_VERSION" ]
then
	MAJOR=$(echo $LLVM_VERSION | cut -d'.' -f1)
	MINOR=$(echo $LLVM_VERSION | cut -d'.' -f2)

	if [ $MAJOR -eq 3 ] && [ $MINOR -le 4 ]
	then
		if [ -f "$1" ]
		then
			filename=$(basename "$1")
			extension="${filename##*.}"

			case "$extension" in
				"gcno") exec $LLVM_COV_BIN --gcno="$1" ;;
				"gcda") exec $LLVM_COV_BIN --gcda="$1" ;;
			esac
		fi
	fi

	if [ $MAJOR -eq 3 ] && [ $MINOR -le 5 ]
	then
		exec $LLVM_COV_BIN $@
	fi
fi

exec $LLVM_COV_BIN gcov $@

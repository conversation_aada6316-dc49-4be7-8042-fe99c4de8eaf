name: Linux builds (basic)

on: [push, pull_request]

jobs:
  build:
    name: ${{matrix.cxx}}, C++${{matrix.std}}, ${{matrix.build_type}}
    runs-on: ubuntu-20.04
    strategy:
      matrix:
        cxx:
          - g++-5
          - g++-6
          - g++-7
          - g++-8
          - g++-9
          - g++-10
          - clang++-6.0
          - clang++-7
          - clang++-8
          - clang++-9
          - clang++-10
        build_type: [Debug, Release]
        std: [14]
        include:
          - cxx: g++-5
            other_pkgs: g++-5
          - cxx: g++-6
            other_pkgs: g++-6
          - cxx: g++-7
            other_pkgs: g++-7
          - cxx: g++-8
            other_pkgs: g++-8
          - cxx: g++-9
            other_pkgs: g++-9
          - cxx: g++-10
            other_pkgs: g++-10
          - cxx: clang++-6.0
            other_pkgs: clang-6.0
          - cxx: clang++-7
            other_pkgs: clang-7
          - cxx: clang++-8
            other_pkgs: clang-8
          - cxx: clang++-9
            other_pkgs: clang-9
          - cxx: clang++-10
            other_pkgs: clang-10
          # Clang 6 + C++17
          # does not work with the default libstdc++ version thanks
          # to a disagreement on variant implementation.
          # - cxx: clang++-6.0
          #   build_type: Debug
          #   std: 17
          #   other_pkgs: clang-6.0
          # - cxx: clang++-6.0
          #   build_type: Release
          #   std: 17
          #   other_pkgs: clang-6.0
          # Clang 10 + C++17
          - cxx: clang++-10
            build_type: Debug
            std: 17
            other_pkgs: clang-10
          - cxx: clang++-10
            build_type: Release
            std: 17
            other_pkgs: clang-10
          - cxx: clang++-10
            build_type: Debug
            std: 20
            other_pkgs: clang-10
          - cxx: clang++-10
            build_type: Release
            std: 20
            other_pkgs: clang-10
          - cxx: g++-10
            build_type: Debug
            std: 20
            other_pkgs: g++-10
          - cxx: g++-10
            build_type: Release
            std: 20
            other_pkgs: g++-10

    steps:
    - uses: actions/checkout@v2

    - name: Add repositories for older GCC
      run: |
        sudo apt-add-repository 'deb http://azure.archive.ubuntu.com/ubuntu/ bionic main'
        sudo apt-add-repository 'deb http://azure.archive.ubuntu.com/ubuntu/ bionic universe'
      if: ${{ matrix.cxx == 'g++-5' || matrix.cxx == 'g++-6' }}

    - name: Prepare environment
      run: sudo apt-get install -y ninja-build ${{matrix.other_pkgs}}

    - name: Configure build
      working-directory: ${{runner.workspace}}
      env:
        CXX: ${{matrix.cxx}}
        CXXFLAGS: ${{matrix.cxxflags}}
      # Note: $GITHUB_WORKSPACE is distinct from ${{runner.workspace}}.
      #       This is important
      run: |
        cmake -Bbuild -H$GITHUB_WORKSPACE \
              -DCMAKE_BUILD_TYPE=${{matrix.build_type}} \
              -DCMAKE_CXX_STANDARD=${{matrix.std}} \
              -DCMAKE_CXX_STANDARD_REQUIRED=ON \
              -DCMAKE_CXX_EXTENSIONS=OFF \
              -DCATCH_DEVELOPMENT_BUILD=ON \
              -G Ninja

    - name: Build tests + lib
      working-directory: ${{runner.workspace}}/build
      run: ninja

    - name: Run tests
      env:
          CTEST_OUTPUT_ON_FAILURE: 1
      working-directory: ${{runner.workspace}}/build
      # Hardcode 2 cores we know are there
      run: ctest -C ${{matrix.build_type}} -j 2

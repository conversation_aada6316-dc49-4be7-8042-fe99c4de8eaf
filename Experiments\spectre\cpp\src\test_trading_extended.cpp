/**
 * @file test_trading_extended.cpp
 * @brief Extended unit tests for trading module including <PERSON>lotter and Algorithm
 * <AUTHOR> from Python implementation by <PERSON><PERSON><PERSON><PERSON> (<PERSON>)
 * @copyright Copyright 2019-2020, Heerozh. All rights reserved.
 * @license Apache 2.0
 */

#include "trading/Trading.h"
#include <iostream>
#include <cassert>
#include <cmath>
#include <chrono>

using namespace Spectre::Trading;

void test_commission_model() {
    std::cout << "Testing CommissionModel..." << std::endl;
    
    CommissionModel commission(0.001, 0.01, 1.0); // 0.1% + $0.01/share, min $1
    
    // Test commission calculation
    double comm = commission.calculate("AAPL", 100.0, 100);
    double expected = std::max(100.0 * 100 * 0.001 + 100 * 0.01, 1.0); // $11
    assert(std::abs(comm - expected) < 1e-6);
    
    // Test empty commission
    CommissionModel empty_commission;
    assert(empty_commission.is_empty());
    assert(empty_commission.calculate("AAPL", 100.0, 100) == 0.0);
    
    std::cout << "CommissionModel tests passed!" << std::endl;
}

void test_slippage_model() {
    std::cout << "Testing SlippageModel..." << std::endl;
    
    SlippageModel slippage(0.001, 0.0002); // 0.1% max slippage, 0.02% volume ratio
    
    // Test slippage calculation
    double adjusted_price = slippage.calculate_price("AAPL", 100.0, 0.0001);
    assert(adjusted_price >= 100.0); // Should add slippage
    
    std::cout << "SlippageModel tests passed!" << std::endl;
}

void test_simulation_blotter() {
    std::cout << "Testing SimulationBlotter..." << std::endl;
    
    auto blotter = std::make_shared<SimulationBlotter>(10000.0);
    auto now = std::chrono::system_clock::now();
    
    // Set up market data
    blotter->set_datetime(now);
    blotter->set_current_prices({{"AAPL", 150.0}, {"GOOGL", 2500.0}});
    blotter->set_current_volumes({{"AAPL", 1000000.0}, {"GOOGL", 500000.0}});
    
    // Test market hours check (should fail initially)
    try {
        blotter->get_price("AAPL");
        assert(false); // Should throw exception
    } catch (const std::runtime_error&) {
        // Expected
    }
    
    // Open market
    blotter->market_open(nullptr);
    
    // Test price retrieval
    double price = blotter->get_price("AAPL");
    assert(std::abs(price - 150.0) < 1e-6);
    
    // Test ordering
    bool success = blotter->order("AAPL", 100);
    assert(success);
    assert(blotter->portfolio().shares("AAPL") == 100);
    
    // Test order target
    success = blotter->order_target("GOOGL", 4);
    assert(success);
    assert(blotter->portfolio().shares("GOOGL") == 4);
    
    // Test order target percent
    success = blotter->order_target_percent("AAPL", 0.5);
    assert(success);
    
    // Test batch ordering
    std::vector<std::string> assets = {"AAPL", "GOOGL"};
    std::vector<double> weights = {0.6, 0.4};
    auto skipped = blotter->batch_order_target_percent(assets, weights);
    
    // Test transaction history
    auto transactions = blotter->get_transactions();
    assert(!transactions.empty());
    
    std::cout << "SimulationBlotter tests passed!" << std::endl;
}

void test_recorder() {
    std::cout << "Testing Recorder..." << std::endl;
    
    Recorder recorder;
    auto now = std::chrono::system_clock::now();
    
    // Record some data
    recorder.record(now, {{"value", 100.0}, {"return", 0.01}});
    recorder.record(now + std::chrono::hours(24), {{"value", 101.0}, {"return", 0.01}});
    
    // Convert to tensors
    auto tensors = recorder.to_tensors();
    assert(tensors.find("value") != tensors.end());
    assert(tensors.find("return") != tensors.end());
    
    auto value_tensor = tensors["value"];
    assert(value_tensor.size(0) == 2);
    
    std::cout << "Recorder tests passed!" << std::endl;
}

void test_buy_and_hold_algorithm() {
    std::cout << "Testing BuyAndHoldAlgorithm..." << std::endl;
    
    auto blotter = std::make_shared<SimulationBlotter>(10000.0);
    std::vector<std::string> assets = {"AAPL", "GOOGL"};
    std::vector<double> weights = {0.6, 0.4};
    
    BuyAndHoldAlgorithm algorithm(blotter, assets, weights);
    
    // Set up market data
    auto now = std::chrono::system_clock::now();
    blotter->set_datetime(now);
    blotter->set_current_prices({{"AAPL", 150.0}, {"GOOGL", 2500.0}});
    blotter->market_open(nullptr);
    
    // Initialize algorithm
    algorithm.initialize();
    
    // Check that positions were created
    assert(blotter->portfolio().has_position("AAPL"));
    assert(blotter->portfolio().has_position("GOOGL"));
    
    std::cout << "BuyAndHoldAlgorithm tests passed!" << std::endl;
}

void test_momentum_algorithm() {
    std::cout << "Testing MomentumAlgorithm..." << std::endl;
    
    auto blotter = std::make_shared<SimulationBlotter>(10000.0);
    MomentumAlgorithm algorithm(blotter, 5, 2); // 5-day lookback, top 2 assets
    
    auto now = std::chrono::system_clock::now();
    blotter->set_datetime(now);
    blotter->market_open(nullptr);
    
    // Feed some price data
    std::unordered_map<std::string, double> day1_data = {
        {"AAPL", 100.0}, {"GOOGL", 2000.0}, {"MSFT", 300.0}
    };
    std::unordered_map<std::string, double> day5_data = {
        {"AAPL", 110.0}, {"GOOGL", 2100.0}, {"MSFT", 290.0}
    };
    
    // Simulate 5 days of data
    for (int i = 0; i < 5; ++i) {
        double factor = static_cast<double>(i) / 4.0;
        std::unordered_map<std::string, double> data;
        for (const auto& [asset, start_price] : day1_data) {
            double end_price = day5_data.at(asset);
            data[asset] = start_price + (end_price - start_price) * factor;
        }
        
        blotter->set_current_prices(data);
        algorithm.handle_data(data);
    }
    
    // Rebalance
    algorithm.rebalance();
    
    // Check that some positions were created
    // AAPL and GOOGL should be selected (positive momentum)
    // MSFT should not be selected (negative momentum)
    assert(blotter->portfolio().has_position("AAPL") || blotter->portfolio().has_position("GOOGL"));
    
    std::cout << "MomentumAlgorithm tests passed!" << std::endl;
}

void test_algorithm_results() {
    std::cout << "Testing AlgorithmResults..." << std::endl;
    
    auto blotter = std::make_shared<SimulationBlotter>(10000.0);
    std::vector<std::string> assets = {"AAPL"};
    std::vector<double> weights = {1.0};
    
    BuyAndHoldAlgorithm algorithm(blotter, assets, weights);
    
    // Set up and run
    auto now = std::chrono::system_clock::now();
    blotter->set_datetime(now);
    blotter->set_current_prices({{"AAPL", 150.0}});
    blotter->market_open(nullptr);
    
    algorithm.initialize();
    
    // Record some data
    algorithm.record({{"custom_metric", 42.0}});
    
    // Get results
    auto results = algorithm.get_results();
    
    // Check results structure
    assert(results.recorded_data.find("custom_metric") != results.recorded_data.end());
    assert(!results.transactions.empty());
    
    std::cout << "AlgorithmResults tests passed!" << std::endl;
}

int main() {
    try {
        std::cout << "Starting Extended Trading Module Tests..." << std::endl;
        
        test_commission_model();
        test_slippage_model();
        test_simulation_blotter();
        test_recorder();
        test_buy_and_hold_algorithm();
        test_momentum_algorithm();
        test_algorithm_results();
        
        std::cout << "All extended tests passed successfully!" << std::endl;
        return 0;
    }
    catch (const std::exception& e) {
        std::cerr << "Test failed with exception: " << e.what() << std::endl;
        return 1;
    }
}

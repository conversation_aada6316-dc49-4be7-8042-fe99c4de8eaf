﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <PreferredToolArchitecture>x64</PreferredToolArchitecture>
  </PropertyGroup>
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="MinSizeRel|x64">
      <Configuration>MinSizeRel</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="RelWithDebInfo|x64">
      <Configuration>RelWithDebInfo</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{8d538cbe-01bf-4a2e-a98a-6c368fdf13d7}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <WindowsTargetPlatformVersion>10.0.26100.0</WindowsTargetPlatformVersion>
    <Platform>x64</Platform>
    <ProjectName>Catch2</ProjectName>
    <VCProjectUpgraderObjectName>NoUpgrade</VCProjectUpgraderObjectName>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup>
    <_ProjectFileVersion>10.0.20506.1</_ProjectFileVersion>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-build\src\Debug\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Catch2.dir\Debug\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Catch2d</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">.lib</TargetExt>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-build\src\Release\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Catch2.dir\Release\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Catch2</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Release|x64'">.lib</TargetExt>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-build\src\MinSizeRel\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">Catch2.dir\MinSizeRel\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">Catch2</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">.lib</TargetExt>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-build\src\RelWithDebInfo\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">Catch2.dir\RelWithDebInfo\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">Catch2</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">.lib</TargetExt>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\..;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-build\generated-includes;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /utf-8 /await</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <ExceptionHandling>Sync</ExceptionHandling>
      <InlineFunctionExpansion>Disabled</InlineFunctionExpansion>
      <LanguageStandard>stdcpp20</LanguageStandard>
      <Optimization>MaxSpeed</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
      <TreatWarningAsError>true</TreatWarningAsError>
      <UseFullPaths>false</UseFullPaths>
      <WarningLevel>Level4</WarningLevel>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;CMAKE_INTDIR="Debug"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_DEBUG;_WINDOWS;NDEBUG;CMAKE_INTDIR=\"Debug\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\..;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-build\generated-includes;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\..;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-build\generated-includes;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Lib>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
    </Lib>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\..;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-build\generated-includes;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /utf-8 /await</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <ExceptionHandling>Sync</ExceptionHandling>
      <InlineFunctionExpansion>AnySuitable</InlineFunctionExpansion>
      <LanguageStandard>stdcpp20</LanguageStandard>
      <Optimization>MaxSpeed</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <TreatWarningAsError>true</TreatWarningAsError>
      <UseFullPaths>false</UseFullPaths>
      <WarningLevel>Level4</WarningLevel>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;CMAKE_INTDIR="Release"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <DebugInformationFormat>
      </DebugInformationFormat>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;CMAKE_INTDIR=\"Release\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\..;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-build\generated-includes;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\..;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-build\generated-includes;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Lib>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
    </Lib>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\..;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-build\generated-includes;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /utf-8 /await</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <ExceptionHandling>Sync</ExceptionHandling>
      <InlineFunctionExpansion>OnlyExplicitInline</InlineFunctionExpansion>
      <LanguageStandard>stdcpp20</LanguageStandard>
      <Optimization>MaxSpeed</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <TreatWarningAsError>true</TreatWarningAsError>
      <UseFullPaths>false</UseFullPaths>
      <WarningLevel>Level4</WarningLevel>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;CMAKE_INTDIR="MinSizeRel"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <DebugInformationFormat>
      </DebugInformationFormat>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;CMAKE_INTDIR=\"MinSizeRel\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\..;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-build\generated-includes;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\..;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-build\generated-includes;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Lib>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
    </Lib>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\..;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-build\generated-includes;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /utf-8 /await</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <ExceptionHandling>Sync</ExceptionHandling>
      <InlineFunctionExpansion>OnlyExplicitInline</InlineFunctionExpansion>
      <LanguageStandard>stdcpp20</LanguageStandard>
      <Optimization>MaxSpeed</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <TreatWarningAsError>true</TreatWarningAsError>
      <UseFullPaths>false</UseFullPaths>
      <WarningLevel>Level4</WarningLevel>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;CMAKE_INTDIR="RelWithDebInfo"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;CMAKE_INTDIR=\"RelWithDebInfo\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\..;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-build\generated-includes;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\..;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-build\generated-includes;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Lib>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
    </Lib>
  </ItemDefinitionGroup>
  <ItemGroup>
    <CustomBuild Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\CMakeLists.txt">
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Building Custom Rule E:/lab/RoboQuant/src/Experiments/DataHub_Modern/build/_deps/catch2-src/src/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
"D:\Program Files\CMake\bin\cmake.exe" -SE:/lab/RoboQuant/src/Experiments/DataHub_Modern -BE:/lab/RoboQuant/src/Experiments/DataHub_Modern/build --check-stamp-file E:/lab/RoboQuant/src/Experiments/DataHub_Modern/build/_deps/catch2-build/src/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:\Program Files\CMake\share\cmake-3.25\Modules\CheckCXXCompilerFlag.cmake;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\CMake\CatchMiscFunctions.cmake;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\catch_user_config.hpp.in;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-build\src\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Building Custom Rule E:/lab/RoboQuant/src/Experiments/DataHub_Modern/build/_deps/catch2-src/src/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">setlocal
"D:\Program Files\CMake\bin\cmake.exe" -SE:/lab/RoboQuant/src/Experiments/DataHub_Modern -BE:/lab/RoboQuant/src/Experiments/DataHub_Modern/build --check-stamp-file E:/lab/RoboQuant/src/Experiments/DataHub_Modern/build/_deps/catch2-build/src/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:\Program Files\CMake\share\cmake-3.25\Modules\CheckCXXCompilerFlag.cmake;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\CMake\CatchMiscFunctions.cmake;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\catch_user_config.hpp.in;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-build\src\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">Building Custom Rule E:/lab/RoboQuant/src/Experiments/DataHub_Modern/build/_deps/catch2-src/src/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">setlocal
"D:\Program Files\CMake\bin\cmake.exe" -SE:/lab/RoboQuant/src/Experiments/DataHub_Modern -BE:/lab/RoboQuant/src/Experiments/DataHub_Modern/build --check-stamp-file E:/lab/RoboQuant/src/Experiments/DataHub_Modern/build/_deps/catch2-build/src/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:\Program Files\CMake\share\cmake-3.25\Modules\CheckCXXCompilerFlag.cmake;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\CMake\CatchMiscFunctions.cmake;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\catch_user_config.hpp.in;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-build\src\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">Building Custom Rule E:/lab/RoboQuant/src/Experiments/DataHub_Modern/build/_deps/catch2-src/src/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">setlocal
"D:\Program Files\CMake\bin\cmake.exe" -SE:/lab/RoboQuant/src/Experiments/DataHub_Modern -BE:/lab/RoboQuant/src/Experiments/DataHub_Modern/build --check-stamp-file E:/lab/RoboQuant/src/Experiments/DataHub_Modern/build/_deps/catch2-build/src/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:\Program Files\CMake\share\cmake-3.25\Modules\CheckCXXCompilerFlag.cmake;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\CMake\CatchMiscFunctions.cmake;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\catch_user_config.hpp.in;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-build\src\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\benchmark\catch_benchmark.hpp" />
    <ClInclude Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\benchmark\catch_benchmark_all.hpp" />
    <ClInclude Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\benchmark\catch_chronometer.hpp" />
    <ClInclude Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\benchmark\catch_clock.hpp" />
    <ClInclude Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\benchmark\catch_constructor.hpp" />
    <ClInclude Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\benchmark\catch_environment.hpp" />
    <ClInclude Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\benchmark\catch_estimate.hpp" />
    <ClInclude Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\benchmark\catch_execution_plan.hpp" />
    <ClInclude Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\benchmark\catch_optimizer.hpp" />
    <ClInclude Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\benchmark\catch_outlier_classification.hpp" />
    <ClInclude Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\benchmark\catch_sample_analysis.hpp" />
    <ClInclude Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\benchmark\detail\catch_analyse.hpp" />
    <ClInclude Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\benchmark\detail\catch_benchmark_function.hpp" />
    <ClInclude Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\benchmark\detail\catch_benchmark_stats.hpp" />
    <ClInclude Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\benchmark\detail\catch_benchmark_stats_fwd.hpp" />
    <ClInclude Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\benchmark\detail\catch_complete_invoke.hpp" />
    <ClInclude Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\benchmark\detail\catch_estimate_clock.hpp" />
    <ClInclude Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\benchmark\detail\catch_measure.hpp" />
    <ClInclude Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\benchmark\detail\catch_repeat.hpp" />
    <ClInclude Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\benchmark\detail\catch_run_for_at_least.hpp" />
    <ClInclude Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\benchmark\detail\catch_stats.hpp" />
    <ClInclude Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\benchmark\detail\catch_timing.hpp" />
    <ClCompile Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\benchmark\catch_chronometer.cpp" />
    <ClCompile Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\benchmark\detail\catch_benchmark_function.cpp" />
    <ClCompile Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\benchmark\detail\catch_run_for_at_least.cpp" />
    <ClCompile Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\benchmark\detail\catch_stats.cpp" />
    <ClInclude Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\generators\catch_generator_exception.hpp" />
    <ClInclude Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\generators\catch_generators.hpp" />
    <ClInclude Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\generators\catch_generators_adapters.hpp" />
    <ClInclude Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\generators\catch_generators_all.hpp" />
    <ClInclude Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\generators\catch_generators_random.hpp" />
    <ClInclude Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\generators\catch_generators_range.hpp" />
    <ClCompile Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\generators\catch_generator_exception.cpp" />
    <ClCompile Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\generators\catch_generators.cpp" />
    <ClCompile Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\generators\catch_generators_random.cpp" />
    <ClInclude Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\reporters\catch_reporter_automake.hpp" />
    <ClInclude Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\reporters\catch_reporter_common_base.hpp" />
    <ClInclude Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\reporters\catch_reporter_compact.hpp" />
    <ClInclude Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\reporters\catch_reporter_console.hpp" />
    <ClInclude Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\reporters\catch_reporter_cumulative_base.hpp" />
    <ClInclude Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\reporters\catch_reporter_event_listener.hpp" />
    <ClInclude Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\reporters\catch_reporter_helpers.hpp" />
    <ClInclude Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\reporters\catch_reporter_junit.hpp" />
    <ClInclude Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\reporters\catch_reporter_multi.hpp" />
    <ClInclude Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\reporters\catch_reporter_registrars.hpp" />
    <ClInclude Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\reporters\catch_reporter_sonarqube.hpp" />
    <ClInclude Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\reporters\catch_reporter_streaming_base.hpp" />
    <ClInclude Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\reporters\catch_reporter_tap.hpp" />
    <ClInclude Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\reporters\catch_reporter_teamcity.hpp" />
    <ClInclude Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\reporters\catch_reporter_xml.hpp" />
    <ClInclude Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\reporters\catch_reporters_all.hpp" />
    <ClCompile Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\reporters\catch_reporter_automake.cpp" />
    <ClCompile Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\reporters\catch_reporter_common_base.cpp" />
    <ClCompile Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\reporters\catch_reporter_compact.cpp" />
    <ClCompile Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\reporters\catch_reporter_console.cpp" />
    <ClCompile Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\reporters\catch_reporter_cumulative_base.cpp" />
    <ClCompile Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\reporters\catch_reporter_event_listener.cpp" />
    <ClCompile Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\reporters\catch_reporter_helpers.cpp" />
    <ClCompile Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\reporters\catch_reporter_junit.cpp" />
    <ClCompile Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\reporters\catch_reporter_multi.cpp" />
    <ClCompile Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\reporters\catch_reporter_registrars.cpp" />
    <ClCompile Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\reporters\catch_reporter_sonarqube.cpp" />
    <ClCompile Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\reporters\catch_reporter_streaming_base.cpp" />
    <ClCompile Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\reporters\catch_reporter_tap.cpp" />
    <ClCompile Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\reporters\catch_reporter_teamcity.cpp" />
    <ClCompile Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\reporters\catch_reporter_xml.cpp" />
    <ClInclude Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\interfaces\catch_interfaces_all.hpp" />
    <ClInclude Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\interfaces\catch_interfaces_capture.hpp" />
    <ClInclude Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\interfaces\catch_interfaces_config.hpp" />
    <ClInclude Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\interfaces\catch_interfaces_enum_values_registry.hpp" />
    <ClInclude Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\interfaces\catch_interfaces_exception.hpp" />
    <ClInclude Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\interfaces\catch_interfaces_generatortracker.hpp" />
    <ClInclude Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\interfaces\catch_interfaces_registry_hub.hpp" />
    <ClInclude Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\interfaces\catch_interfaces_reporter.hpp" />
    <ClInclude Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\interfaces\catch_interfaces_reporter_factory.hpp" />
    <ClInclude Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\interfaces\catch_interfaces_tag_alias_registry.hpp" />
    <ClInclude Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\interfaces\catch_interfaces_test_invoker.hpp" />
    <ClInclude Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\interfaces\catch_interfaces_testcase.hpp" />
    <ClCompile Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\interfaces\catch_interfaces_capture.cpp" />
    <ClCompile Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\interfaces\catch_interfaces_config.cpp" />
    <ClCompile Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\interfaces\catch_interfaces_exception.cpp" />
    <ClCompile Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\interfaces\catch_interfaces_generatortracker.cpp" />
    <ClCompile Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\interfaces\catch_interfaces_registry_hub.cpp" />
    <ClCompile Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\interfaces\catch_interfaces_reporter.cpp" />
    <ClCompile Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\interfaces\catch_interfaces_reporter_factory.cpp" />
    <ClCompile Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\interfaces\catch_interfaces_testcase.cpp" />
    <ClCompile Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\catch_approx.cpp" />
    <ClCompile Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\catch_assertion_result.cpp" />
    <ClCompile Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\catch_config.cpp" />
    <ClCompile Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\catch_get_random_seed.cpp" />
    <ClCompile Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\catch_message.cpp" />
    <ClCompile Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\catch_registry_hub.cpp" />
    <ClCompile Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\catch_session.cpp" />
    <ClCompile Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\catch_tag_alias_autoregistrar.cpp" />
    <ClCompile Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\catch_test_case_info.cpp" />
    <ClCompile Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\catch_test_spec.cpp" />
    <ClCompile Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\catch_timer.cpp" />
    <ClCompile Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\catch_tostring.cpp" />
    <ClCompile Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\catch_totals.cpp" />
    <ClCompile Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\catch_translate_exception.cpp" />
    <ClCompile Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\catch_version.cpp" />
    <ClCompile Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\internal\catch_assertion_handler.cpp" />
    <ClCompile Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\internal\catch_case_insensitive_comparisons.cpp" />
    <ClCompile Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\internal\catch_clara.cpp" />
    <ClCompile Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\internal\catch_commandline.cpp" />
    <ClCompile Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\internal\catch_console_colour.cpp" />
    <ClCompile Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\internal\catch_context.cpp" />
    <ClCompile Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\internal\catch_debug_console.cpp" />
    <ClCompile Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\internal\catch_debugger.cpp" />
    <ClCompile Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\internal\catch_decomposer.cpp" />
    <ClCompile Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\internal\catch_enforce.cpp" />
    <ClCompile Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\internal\catch_enum_values_registry.cpp" />
    <ClCompile Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\internal\catch_errno_guard.cpp" />
    <ClCompile Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\internal\catch_exception_translator_registry.cpp" />
    <ClCompile Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\internal\catch_fatal_condition_handler.cpp" />
    <ClCompile Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\internal\catch_floating_point_helpers.cpp" />
    <ClCompile Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\internal\catch_getenv.cpp" />
    <ClCompile Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\internal\catch_istream.cpp" />
    <ClCompile Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\internal\catch_lazy_expr.cpp" />
    <ClCompile Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\internal\catch_leak_detector.cpp" />
    <ClCompile Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\internal\catch_list.cpp" />
    <ClCompile Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\internal\catch_message_info.cpp" />
    <ClCompile Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\internal\catch_output_redirect.cpp" />
    <ClCompile Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\internal\catch_parse_numbers.cpp" />
    <ClCompile Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\internal\catch_polyfills.cpp" />
    <ClCompile Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\internal\catch_random_number_generator.cpp" />
    <ClCompile Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\internal\catch_random_seed_generation.cpp" />
    <ClCompile Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\internal\catch_reporter_registry.cpp" />
    <ClCompile Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\internal\catch_reporter_spec_parser.cpp" />
    <ClCompile Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\internal\catch_result_type.cpp" />
    <ClCompile Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\internal\catch_reusable_string_stream.cpp" />
    <ClCompile Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\internal\catch_run_context.cpp" />
    <ClCompile Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\internal\catch_section.cpp" />
    <ClCompile Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\internal\catch_singletons.cpp" />
    <ClCompile Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\internal\catch_source_line_info.cpp" />
    <ClCompile Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\internal\catch_startup_exception_registry.cpp" />
    <ClCompile Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\internal\catch_stdstreams.cpp" />
    <ClCompile Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\internal\catch_string_manip.cpp" />
    <ClCompile Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\internal\catch_stringref.cpp" />
    <ClCompile Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\internal\catch_tag_alias_registry.cpp" />
    <ClCompile Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\internal\catch_test_case_info_hasher.cpp" />
    <ClCompile Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\internal\catch_test_case_registry_impl.cpp" />
    <ClCompile Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\internal\catch_test_case_tracker.cpp" />
    <ClCompile Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\internal\catch_test_failure_exception.cpp" />
    <ClCompile Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\internal\catch_test_registry.cpp" />
    <ClCompile Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\internal\catch_test_spec_parser.cpp" />
    <ClCompile Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\internal\catch_textflow.cpp" />
    <ClCompile Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\internal\catch_uncaught_exceptions.cpp" />
    <ClCompile Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\internal\catch_wildcard_pattern.cpp" />
    <ClCompile Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\internal\catch_xmlwriter.cpp" />
    <ClInclude Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-build\generated-includes\catch2\catch_user_config.hpp" />
    <ClInclude Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\catch_user_config.hpp.in" />
    <ClInclude Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\catch_all.hpp" />
    <ClInclude Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\catch_approx.hpp" />
    <ClInclude Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\catch_assertion_info.hpp" />
    <ClInclude Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\catch_assertion_result.hpp" />
    <ClInclude Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\catch_config.hpp" />
    <ClInclude Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\catch_get_random_seed.hpp" />
    <ClInclude Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\catch_message.hpp" />
    <ClInclude Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\catch_section_info.hpp" />
    <ClInclude Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\catch_session.hpp" />
    <ClInclude Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\catch_tag_alias.hpp" />
    <ClInclude Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\catch_tag_alias_autoregistrar.hpp" />
    <ClInclude Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\catch_template_test_macros.hpp" />
    <ClInclude Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\catch_test_case_info.hpp" />
    <ClInclude Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\catch_test_macros.hpp" />
    <ClInclude Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\catch_test_spec.hpp" />
    <ClInclude Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\catch_timer.hpp" />
    <ClInclude Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\catch_tostring.hpp" />
    <ClInclude Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\catch_totals.hpp" />
    <ClInclude Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\catch_translate_exception.hpp" />
    <ClInclude Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\catch_version.hpp" />
    <ClInclude Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\catch_version_macros.hpp" />
    <ClInclude Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\internal\catch_assertion_handler.hpp" />
    <ClInclude Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\internal\catch_case_insensitive_comparisons.hpp" />
    <ClInclude Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\internal\catch_case_sensitive.hpp" />
    <ClInclude Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\internal\catch_clara.hpp" />
    <ClInclude Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\internal\catch_commandline.hpp" />
    <ClInclude Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\internal\catch_compare_traits.hpp" />
    <ClInclude Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\internal\catch_compiler_capabilities.hpp" />
    <ClInclude Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\internal\catch_config_android_logwrite.hpp" />
    <ClInclude Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\internal\catch_config_counter.hpp" />
    <ClInclude Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\internal\catch_config_static_analysis_support.hpp" />
    <ClInclude Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\internal\catch_config_uncaught_exceptions.hpp" />
    <ClInclude Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\internal\catch_config_wchar.hpp" />
    <ClInclude Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\internal\catch_console_colour.hpp" />
    <ClInclude Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\internal\catch_console_width.hpp" />
    <ClInclude Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\internal\catch_container_nonmembers.hpp" />
    <ClInclude Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\internal\catch_context.hpp" />
    <ClInclude Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\internal\catch_debug_console.hpp" />
    <ClInclude Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\internal\catch_debugger.hpp" />
    <ClInclude Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\internal\catch_decomposer.hpp" />
    <ClInclude Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\internal\catch_enforce.hpp" />
    <ClInclude Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\internal\catch_enum_values_registry.hpp" />
    <ClInclude Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\internal\catch_errno_guard.hpp" />
    <ClInclude Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\internal\catch_exception_translator_registry.hpp" />
    <ClInclude Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\internal\catch_fatal_condition_handler.hpp" />
    <ClInclude Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\internal\catch_floating_point_helpers.hpp" />
    <ClInclude Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\internal\catch_getenv.hpp" />
    <ClInclude Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\internal\catch_istream.hpp" />
    <ClInclude Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\internal\catch_is_permutation.hpp" />
    <ClInclude Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\internal\catch_lazy_expr.hpp" />
    <ClInclude Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\internal\catch_leak_detector.hpp" />
    <ClInclude Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\internal\catch_list.hpp" />
    <ClInclude Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\internal\catch_logical_traits.hpp" />
    <ClInclude Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\internal\catch_message_info.hpp" />
    <ClInclude Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\internal\catch_meta.hpp" />
    <ClInclude Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\internal\catch_move_and_forward.hpp" />
    <ClInclude Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\internal\catch_noncopyable.hpp" />
    <ClInclude Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\internal\catch_optional.hpp" />
    <ClInclude Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\internal\catch_output_redirect.hpp" />
    <ClInclude Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\internal\catch_parse_numbers.hpp" />
    <ClInclude Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\internal\catch_platform.hpp" />
    <ClInclude Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\internal\catch_polyfills.hpp" />
    <ClInclude Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\internal\catch_preprocessor.hpp" />
    <ClInclude Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\internal\catch_preprocessor_remove_parens.hpp" />
    <ClInclude Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\internal\catch_random_number_generator.hpp" />
    <ClInclude Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\internal\catch_random_seed_generation.hpp" />
    <ClInclude Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\internal\catch_reporter_registry.hpp" />
    <ClInclude Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\internal\catch_reporter_spec_parser.hpp" />
    <ClInclude Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\internal\catch_result_type.hpp" />
    <ClInclude Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\internal\catch_reusable_string_stream.hpp" />
    <ClInclude Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\internal\catch_run_context.hpp" />
    <ClInclude Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\internal\catch_section.hpp" />
    <ClInclude Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\internal\catch_sharding.hpp" />
    <ClInclude Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\internal\catch_singletons.hpp" />
    <ClInclude Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\internal\catch_source_line_info.hpp" />
    <ClInclude Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\internal\catch_startup_exception_registry.hpp" />
    <ClInclude Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\internal\catch_stdstreams.hpp" />
    <ClInclude Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\internal\catch_stream_end_stop.hpp" />
    <ClInclude Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\internal\catch_string_manip.hpp" />
    <ClInclude Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\internal\catch_stringref.hpp" />
    <ClInclude Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\internal\catch_tag_alias_registry.hpp" />
    <ClInclude Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\internal\catch_template_test_registry.hpp" />
    <ClInclude Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\internal\catch_test_case_info_hasher.hpp" />
    <ClInclude Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\internal\catch_test_case_registry_impl.hpp" />
    <ClInclude Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\internal\catch_test_case_tracker.hpp" />
    <ClInclude Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\internal\catch_test_failure_exception.hpp" />
    <ClInclude Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\internal\catch_test_macro_impl.hpp" />
    <ClInclude Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\internal\catch_test_registry.hpp" />
    <ClInclude Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\internal\catch_test_run_info.hpp" />
    <ClInclude Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\internal\catch_test_spec_parser.hpp" />
    <ClInclude Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\internal\catch_textflow.hpp" />
    <ClInclude Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\internal\catch_to_string.hpp" />
    <ClInclude Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\internal\catch_uncaught_exceptions.hpp" />
    <ClInclude Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\internal\catch_unique_name.hpp" />
    <ClInclude Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\internal\catch_unique_ptr.hpp" />
    <ClInclude Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\internal\catch_void_type.hpp" />
    <ClInclude Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\internal\catch_wildcard_pattern.hpp" />
    <ClInclude Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\internal\catch_windows_h_proxy.hpp" />
    <ClInclude Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\internal\catch_xmlwriter.hpp" />
    <ClInclude Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\matchers\catch_matchers.hpp" />
    <ClInclude Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\matchers\catch_matchers_all.hpp" />
    <ClInclude Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\matchers\catch_matchers_container_properties.hpp" />
    <ClInclude Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\matchers\catch_matchers_contains.hpp" />
    <ClInclude Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\matchers\catch_matchers_range_equals.hpp" />
    <ClInclude Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\matchers\catch_matchers_exception.hpp" />
    <ClInclude Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\matchers\catch_matchers_floating_point.hpp" />
    <ClInclude Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\matchers\catch_matchers_predicate.hpp" />
    <ClInclude Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\matchers\catch_matchers_quantifiers.hpp" />
    <ClInclude Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\matchers\catch_matchers_string.hpp" />
    <ClInclude Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\matchers\catch_matchers_templated.hpp" />
    <ClInclude Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\matchers\catch_matchers_vector.hpp" />
    <ClInclude Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\matchers\internal\catch_matchers_impl.hpp" />
    <ClCompile Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\matchers\catch_matchers.cpp" />
    <ClCompile Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\matchers\catch_matchers_container_properties.cpp" />
    <ClCompile Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\matchers\catch_matchers_exception.cpp" />
    <ClCompile Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\matchers\catch_matchers_floating_point.cpp" />
    <ClCompile Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\matchers\catch_matchers_predicate.cpp" />
    <ClCompile Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\matchers\catch_matchers_quantifiers.cpp" />
    <ClCompile Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\matchers\catch_matchers_string.cpp" />
    <ClCompile Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\matchers\catch_matchers_templated.cpp" />
    <ClCompile Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src\src\catch2\matchers\internal\catch_matchers_impl.cpp" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\ZERO_CHECK.vcxproj">
      <Project>{E845B234-6F99-32FB-9478-5DF9566AB4BB}</Project>
      <Name>ZERO_CHECK</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>
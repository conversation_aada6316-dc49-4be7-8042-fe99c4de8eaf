RUN: rm -rf %tmp
RUN: mkdir %tmp && cd %tmp
# Create a shared corpus directory
RUN: rm -rf FuzzerJobsTestCORPUS
RUN: mkdir FuzzerJobsTestCORPUS
RUN: rm -f fuzz-{0,1}.log
# Start fuzzer and in parallel check that the output files
# that should be created exist.
RUN: LLVMFuzzer-EmptyTest -max_total_time=4 -jobs=2 -workers=2 FuzzerJobsTestCORPUS > %t-fuzzer-jobs-test.log 2>&1 & export FUZZER_PID=$!
# Wait a short while to give time for the child processes
# to start fuzzing
RUN: sleep 2
# If the instances are running in parallel they should have created their log
# files by now.
RUN: ls fuzz-0.log
RUN: ls fuzz-1.log
# Wait for libfuzzer to finish.
# This probably isn't portable but we need a way to block until
# the fuzzer is done otherwise we might remove the files while
# they are being used.
RUN: while kill -0 ${FUZZER_PID}; do : ; done
RUN: rm -f fuzz-{0,1}.log
RUN: rm -rf FuzzerJobsTestCORPUS
RUN: FileCheck -input-file=%t-fuzzer-jobs-test.log %s
RUN: rm %t-fuzzer-jobs-test.log
RUN: cd ../

CHECK-DAG: Job 0 exited with exit code 0
CHECK-DAG: Job 1 exited with exit code 0

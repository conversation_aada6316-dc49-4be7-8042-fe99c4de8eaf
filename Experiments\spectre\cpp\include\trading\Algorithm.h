#pragma once

/**
 * @file Algorithm.h
 * @brief Base class for custom trading algorithms
 * <AUTHOR> from Python implementation by <PERSON><PERSON><PERSON><PERSON> (<PERSON>)
 * @copyright Copyright 2019-2020, <PERSON><PERSON>zh. All rights reserved.
 * @license Apache 2.0
 */

#include "Blotter.h"
#include "Event.h"
#include <torch/torch.h>
#include <unordered_map>
#include <vector>
#include <memory>
#include <functional>
#include <chrono>

namespace Spectre {
namespace Trading {

// Forward declarations
class DataLoader;
class FactorEngine;

/**
 * @brief Recorder for algorithm data
 */
class Recorder {
public:
    using TimePoint = std::chrono::system_clock::time_point;
    using RecordMap = std::unordered_map<std::string, double>;

    /**
     * @brief Constructor
     */
    Recorder() = default;

    /**
     * @brief Record data for a specific date
     * @param date Recording date
     * @param data Data to record (key-value pairs)
     */
    void record(const TimePoint& date, const RecordMap& data);

    /**
     * @brief Get all recorded data as tensors
     * @return Map of data name to tensor
     */
    std::unordered_map<std::string, torch::Tensor> to_tensors() const;

    /**
     * @brief Get recorded dates
     * @return Vector of recorded dates
     */
    std::vector<TimePoint> get_dates() const;

    /**
     * @brief Clear all recorded data
     */
    void clear();

private:
    struct Record {
        TimePoint date;
        RecordMap data;
    };
    
    std::vector<Record> records_;
};

/**
 * @brief Algorithm results structure
 */
struct AlgorithmResults {
    torch::Tensor returns;
    std::vector<Portfolio::HistoryRecord> positions;
    std::vector<Order> transactions;
    std::unordered_map<std::string, torch::Tensor> recorded_data;

    AlgorithmResults() = default;
    AlgorithmResults(const torch::Tensor& ret, 
                    const std::vector<Portfolio::HistoryRecord>& pos,
                    const std::vector<Order>& trans,
                    const std::unordered_map<std::string, torch::Tensor>& data)
        : returns(ret), positions(pos), transactions(trans), recorded_data(data) {}
};

/**
 * @brief Base class for custom trading algorithms
 */
class CustomAlgorithm : public EventReceiver {
public:
    using TimePoint = std::chrono::system_clock::time_point;
    using Duration = std::chrono::duration<double>;

    /**
     * @brief Constructor
     * @param blotter Order management system
     */
    explicit CustomAlgorithm(std::shared_ptr<BaseBlotter> blotter);

    /**
     * @brief Destructor
     */
    virtual ~CustomAlgorithm() = default;

    // Getters
    std::shared_ptr<BaseBlotter> blotter() const { return blotter_; }
    const Recorder& recorder() const { return recorder_; }
    TimePoint current_dt() const { return current_dt_; }
    bool delay_factor() const { return delay_factor_; }
    const AlgorithmResults& results() const { return results_; }

    // Setters
    void set_delay_factor(bool delay) { delay_factor_ = delay; }

    /**
     * @brief Clear algorithm state
     */
    virtual void clear();

    /**
     * @brief Set current datetime
     * @param dt New datetime
     */
    virtual void set_datetime(const TimePoint& dt);

    /**
     * @brief Set history window for data lookback
     * @param window History window duration
     */
    void set_history_window(const Duration& window);

    /**
     * @brief Record data for current datetime
     * @param data Data to record
     */
    void record(const std::unordered_map<std::string, double>& data);

    /**
     * @brief Set rebalance callback function
     * @param callback Function to call for rebalancing
     */
    void set_rebalance_callback(std::function<void()> callback);

    /**
     * @brief Get algorithm results
     * @return Algorithm results structure
     */
    AlgorithmResults get_results();

    // Virtual methods to be implemented by derived classes
    
    /**
     * @brief Initialize algorithm (called once at start)
     */
    virtual void initialize() {}

    /**
     * @brief Handle new data (called on every bar)
     * @param data Current market data
     */
    virtual void handle_data(const std::unordered_map<std::string, double>& data) {}

    /**
     * @brief Rebalance portfolio (called at specified intervals)
     */
    virtual void rebalance() {}

    /**
     * @brief Before market open (called before market opens)
     */
    virtual void before_market_open() {}

    /**
     * @brief After market close (called after market closes)
     */
    virtual void after_market_close() {}

    /**
     * @brief Finalize algorithm (called once at end)
     */
    virtual void finalize() {}

    // EventReceiver interface
    void on_run() override;
    void on_end_of_run() override;

protected:
    std::shared_ptr<BaseBlotter> blotter_;
    Recorder recorder_;
    TimePoint current_dt_;
    Duration history_window_;
    bool delay_factor_;
    AlgorithmResults results_;
    std::function<void()> rebalance_callback_;

    // Current market data
    std::unordered_map<std::string, double> current_data_;

    /**
     * @brief Internal event handlers
     */
    void on_market_open(EventReceiver* source);
    void on_market_close(EventReceiver* source);
    void on_new_data(EventReceiver* source);
    void on_rebalance(EventReceiver* source);
};

/**
 * @brief Simple buy-and-hold algorithm example
 */
class BuyAndHoldAlgorithm : public CustomAlgorithm {
public:
    /**
     * @brief Constructor
     * @param blotter Order management system
     * @param assets Assets to buy and hold
     * @param weights Portfolio weights (must sum to 1.0)
     */
    BuyAndHoldAlgorithm(std::shared_ptr<BaseBlotter> blotter,
                       const std::vector<std::string>& assets,
                       const std::vector<double>& weights);

    void initialize() override;
    void rebalance() override;

private:
    std::vector<std::string> assets_;
    std::vector<double> weights_;
    bool initialized_;
};

/**
 * @brief Momentum algorithm example
 */
class MomentumAlgorithm : public CustomAlgorithm {
public:
    /**
     * @brief Constructor
     * @param blotter Order management system
     * @param lookback_days Lookback period for momentum calculation
     * @param top_n Number of top momentum stocks to hold
     */
    MomentumAlgorithm(std::shared_ptr<BaseBlotter> blotter,
                     int lookback_days = 20,
                     int top_n = 10);

    void initialize() override;
    void handle_data(const std::unordered_map<std::string, double>& data) override;
    void rebalance() override;

private:
    int lookback_days_;
    int top_n_;
    std::unordered_map<std::string, std::vector<double>> price_history_;
    
    /**
     * @brief Calculate momentum for an asset
     * @param asset Asset symbol
     * @return Momentum score
     */
    double calculate_momentum(const std::string& asset) const;
    
    /**
     * @brief Select top momentum assets
     * @return Vector of selected assets
     */
    std::vector<std::string> select_assets() const;
};

/**
 * @brief Helper functions for running algorithms
 */
namespace AlgorithmUtils {

/**
 * @brief Run backtest for an algorithm
 * @param algorithm Algorithm to run
 * @param start_date Start date for backtest
 * @param end_date End date for backtest
 * @param data_source Data source for prices
 * @return Algorithm results
 */
AlgorithmResults run_backtest(
    std::shared_ptr<CustomAlgorithm> algorithm,
    const std::chrono::system_clock::time_point& start_date,
    const std::chrono::system_clock::time_point& end_date,
    const std::unordered_map<std::string, std::vector<std::pair<std::chrono::system_clock::time_point, double>>>& data_source
);

/**
 * @brief Create a simple data source from price data
 * @param prices Map of asset to (date, price) pairs
 * @return Data source suitable for backtesting
 */
std::unordered_map<std::string, std::vector<std::pair<std::chrono::system_clock::time_point, double>>>
create_data_source(const std::unordered_map<std::string, std::vector<std::pair<std::chrono::system_clock::time_point, double>>>& prices);

} // namespace AlgorithmUtils

} // namespace Trading
} // namespace Spectre

﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <PreferredToolArchitecture>x64</PreferredToolArchitecture>
  </PropertyGroup>
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="MinSizeRel|x64">
      <Configuration>MinSizeRel</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="RelWithDebInfo|x64">
      <Configuration>RelWithDebInfo</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{1D940A27-C1DF-3DD0-945E-F6A321144F84}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <WindowsTargetPlatformVersion>10.0.26100.0</WindowsTargetPlatformVersion>
    <Platform>x64</Platform>
    <ProjectName>quote_example</ProjectName>
    <VCProjectUpgraderObjectName>NoUpgrade</VCProjectUpgraderObjectName>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup>
    <_ProjectFileVersion>10.0.20506.1</_ProjectFileVersion>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\examples\Debug\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">quote_example.dir\Debug\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">quote_example</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">.exe</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</GenerateManifest>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\examples\Release\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">quote_example.dir\Release\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Release|x64'">quote_example</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Release|x64'">.exe</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</GenerateManifest>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\examples\MinSizeRel\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">quote_example.dir\MinSizeRel\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">quote_example</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">.exe</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">false</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">true</GenerateManifest>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\examples\RelWithDebInfo\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">quote_example.dir\RelWithDebInfo\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">quote_example</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">.exe</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">true</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">true</GenerateManifest>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>E:\lab\RoboQuant\src\Experiments\DataHub_Modern\include;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\spdlog-src\include;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\nlohmann_json-src\include;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\fmt-src\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /utf-8 /await</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <ExceptionHandling>Sync</ExceptionHandling>
      <InlineFunctionExpansion>Disabled</InlineFunctionExpansion>
      <LanguageStandard>stdcpp20</LanguageStandard>
      <Optimization>MaxSpeed</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
      <TreatWarningAsError>true</TreatWarningAsError>
      <UseFullPaths>false</UseFullPaths>
      <WarningLevel>Level4</WarningLevel>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;SPDLOG_COMPILED_LIB;CMAKE_INTDIR="Debug"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_DEBUG;_WINDOWS;NDEBUG;SPDLOG_COMPILED_LIB;CMAKE_INTDIR=\"Debug\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>E:\lab\RoboQuant\src\Experiments\DataHub_Modern\include;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\spdlog-src\include;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\nlohmann_json-src\include;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\fmt-src\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>E:\lab\RoboQuant\src\Experiments\DataHub_Modern\include;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\spdlog-src\include;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\nlohmann_json-src\include;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\fmt-src\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Link>
      <AdditionalDependencies>..\Debug\datahub.lib;..\Debug\datahub_api.lib;..\Debug\datahub_services.lib;..\Debug\datahub_data.lib;..\Debug\datahub_core.lib;..\_deps\spdlog-build\Debug\spdlogd.lib;..\_deps\fmt-build\Debug\fmtd.lib;-lpthreads;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>E:/lab/RoboQuant/src/Experiments/DataHub_Modern/build/examples/Debug/quote_example.lib</ImportLibrary>
      <ProgramDataBaseFile>E:/lab/RoboQuant/src/Experiments/DataHub_Modern/build/examples/Debug/quote_example.pdb</ProgramDataBaseFile>
      <SubSystem>Console</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>E:\lab\RoboQuant\src\Experiments\DataHub_Modern\include;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\spdlog-src\include;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\nlohmann_json-src\include;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\fmt-src\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /utf-8 /await</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <ExceptionHandling>Sync</ExceptionHandling>
      <InlineFunctionExpansion>AnySuitable</InlineFunctionExpansion>
      <LanguageStandard>stdcpp20</LanguageStandard>
      <Optimization>MaxSpeed</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <TreatWarningAsError>true</TreatWarningAsError>
      <UseFullPaths>false</UseFullPaths>
      <WarningLevel>Level4</WarningLevel>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;SPDLOG_COMPILED_LIB;CMAKE_INTDIR="Release"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <DebugInformationFormat>
      </DebugInformationFormat>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;SPDLOG_COMPILED_LIB;CMAKE_INTDIR=\"Release\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>E:\lab\RoboQuant\src\Experiments\DataHub_Modern\include;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\spdlog-src\include;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\nlohmann_json-src\include;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\fmt-src\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>E:\lab\RoboQuant\src\Experiments\DataHub_Modern\include;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\spdlog-src\include;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\nlohmann_json-src\include;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\fmt-src\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Link>
      <AdditionalDependencies>..\Release\datahub.lib;..\Release\datahub_api.lib;..\Release\datahub_services.lib;..\Release\datahub_data.lib;..\Release\datahub_core.lib;..\_deps\spdlog-build\Release\spdlog.lib;..\_deps\fmt-build\Release\fmt.lib;-lpthreads;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>false</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>E:/lab/RoboQuant/src/Experiments/DataHub_Modern/build/examples/Release/quote_example.lib</ImportLibrary>
      <ProgramDataBaseFile>E:/lab/RoboQuant/src/Experiments/DataHub_Modern/build/examples/Release/quote_example.pdb</ProgramDataBaseFile>
      <SubSystem>Console</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>E:\lab\RoboQuant\src\Experiments\DataHub_Modern\include;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\spdlog-src\include;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\nlohmann_json-src\include;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\fmt-src\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /utf-8 /await</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <ExceptionHandling>Sync</ExceptionHandling>
      <InlineFunctionExpansion>OnlyExplicitInline</InlineFunctionExpansion>
      <LanguageStandard>stdcpp20</LanguageStandard>
      <Optimization>MaxSpeed</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <TreatWarningAsError>true</TreatWarningAsError>
      <UseFullPaths>false</UseFullPaths>
      <WarningLevel>Level4</WarningLevel>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;SPDLOG_COMPILED_LIB;CMAKE_INTDIR="MinSizeRel"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <DebugInformationFormat>
      </DebugInformationFormat>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;SPDLOG_COMPILED_LIB;CMAKE_INTDIR=\"MinSizeRel\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>E:\lab\RoboQuant\src\Experiments\DataHub_Modern\include;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\spdlog-src\include;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\nlohmann_json-src\include;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\fmt-src\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>E:\lab\RoboQuant\src\Experiments\DataHub_Modern\include;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\spdlog-src\include;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\nlohmann_json-src\include;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\fmt-src\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Link>
      <AdditionalDependencies>..\MinSizeRel\datahub.lib;..\MinSizeRel\datahub_api.lib;..\MinSizeRel\datahub_services.lib;..\MinSizeRel\datahub_data.lib;..\MinSizeRel\datahub_core.lib;..\_deps\spdlog-build\MinSizeRel\spdlog.lib;..\_deps\fmt-build\MinSizeRel\fmt.lib;-lpthreads;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>false</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>E:/lab/RoboQuant/src/Experiments/DataHub_Modern/build/examples/MinSizeRel/quote_example.lib</ImportLibrary>
      <ProgramDataBaseFile>E:/lab/RoboQuant/src/Experiments/DataHub_Modern/build/examples/MinSizeRel/quote_example.pdb</ProgramDataBaseFile>
      <SubSystem>Console</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>E:\lab\RoboQuant\src\Experiments\DataHub_Modern\include;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\spdlog-src\include;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\nlohmann_json-src\include;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\fmt-src\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /utf-8 /await</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <ExceptionHandling>Sync</ExceptionHandling>
      <InlineFunctionExpansion>OnlyExplicitInline</InlineFunctionExpansion>
      <LanguageStandard>stdcpp20</LanguageStandard>
      <Optimization>MaxSpeed</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <TreatWarningAsError>true</TreatWarningAsError>
      <UseFullPaths>false</UseFullPaths>
      <WarningLevel>Level4</WarningLevel>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;SPDLOG_COMPILED_LIB;CMAKE_INTDIR="RelWithDebInfo"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;SPDLOG_COMPILED_LIB;CMAKE_INTDIR=\"RelWithDebInfo\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>E:\lab\RoboQuant\src\Experiments\DataHub_Modern\include;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\spdlog-src\include;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\nlohmann_json-src\include;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\fmt-src\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>E:\lab\RoboQuant\src\Experiments\DataHub_Modern\include;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\spdlog-src\include;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\nlohmann_json-src\include;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\fmt-src\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Link>
      <AdditionalDependencies>..\RelWithDebInfo\datahub.lib;..\RelWithDebInfo\datahub_api.lib;..\RelWithDebInfo\datahub_services.lib;..\RelWithDebInfo\datahub_data.lib;..\RelWithDebInfo\datahub_core.lib;..\_deps\spdlog-build\RelWithDebInfo\spdlog.lib;..\_deps\fmt-build\RelWithDebInfo\fmt.lib;-lpthreads;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>E:/lab/RoboQuant/src/Experiments/DataHub_Modern/build/examples/RelWithDebInfo/quote_example.lib</ImportLibrary>
      <ProgramDataBaseFile>E:/lab/RoboQuant/src/Experiments/DataHub_Modern/build/examples/RelWithDebInfo/quote_example.pdb</ProgramDataBaseFile>
      <SubSystem>Console</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemGroup>
    <CustomBuild Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\examples\CMakeLists.txt">
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Building Custom Rule E:/lab/RoboQuant/src/Experiments/DataHub_Modern/examples/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
"D:\Program Files\CMake\bin\cmake.exe" -SE:/lab/RoboQuant/src/Experiments/DataHub_Modern -BE:/lab/RoboQuant/src/Experiments/DataHub_Modern/build --check-stamp-file E:/lab/RoboQuant/src/Experiments/DataHub_Modern/build/examples/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\examples\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Building Custom Rule E:/lab/RoboQuant/src/Experiments/DataHub_Modern/examples/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">setlocal
"D:\Program Files\CMake\bin\cmake.exe" -SE:/lab/RoboQuant/src/Experiments/DataHub_Modern -BE:/lab/RoboQuant/src/Experiments/DataHub_Modern/build --check-stamp-file E:/lab/RoboQuant/src/Experiments/DataHub_Modern/build/examples/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\examples\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">Building Custom Rule E:/lab/RoboQuant/src/Experiments/DataHub_Modern/examples/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">setlocal
"D:\Program Files\CMake\bin\cmake.exe" -SE:/lab/RoboQuant/src/Experiments/DataHub_Modern -BE:/lab/RoboQuant/src/Experiments/DataHub_Modern/build --check-stamp-file E:/lab/RoboQuant/src/Experiments/DataHub_Modern/build/examples/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\examples\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">Building Custom Rule E:/lab/RoboQuant/src/Experiments/DataHub_Modern/examples/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">setlocal
"D:\Program Files\CMake\bin\cmake.exe" -SE:/lab/RoboQuant/src/Experiments/DataHub_Modern -BE:/lab/RoboQuant/src/Experiments/DataHub_Modern/build --check-stamp-file E:/lab/RoboQuant/src/Experiments/DataHub_Modern/build/examples/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\examples\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\examples\quote_example.cpp" />
    <Natvis Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\nlohmann_json-src\nlohmann_json.natvis">
    </Natvis>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\ZERO_CHECK.vcxproj">
      <Project>{E845B234-6F99-32FB-9478-5DF9566AB4BB}</Project>
      <Name>ZERO_CHECK</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
    <ProjectReference Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\datahub.vcxproj">
      <Project>{DB9E74C7-04F2-3377-931B-70D824069E69}</Project>
      <Name>datahub</Name>
    </ProjectReference>
    <ProjectReference Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\datahub_api.vcxproj">
      <Project>{CB51F203-F8B5-3262-B49C-B618BAFF21A1}</Project>
      <Name>datahub_api</Name>
    </ProjectReference>
    <ProjectReference Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\datahub_core.vcxproj">
      <Project>{BBD6BED9-52DD-3B42-A3C8-1B2962C824CD}</Project>
      <Name>datahub_core</Name>
    </ProjectReference>
    <ProjectReference Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\datahub_data.vcxproj">
      <Project>{9F8C1425-A454-3C64-BD6B-B06574E87961}</Project>
      <Name>datahub_data</Name>
    </ProjectReference>
    <ProjectReference Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\datahub_services.vcxproj">
      <Project>{4F6C7749-5D04-3C41-A3C0-42468712FF5F}</Project>
      <Name>datahub_services</Name>
    </ProjectReference>
    <ProjectReference Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\fmt-build\fmt.vcxproj">
      <Project>{E14E2F0A-5EE3-3D20-8A77-533866B525A6}</Project>
      <Name>fmt</Name>
    </ProjectReference>
    <ProjectReference Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\spdlog-build\spdlog.vcxproj">
      <Project>{BD106DC0-635E-3CCE-8587-9070DE8C267B}</Project>
      <Name>spdlog</Name>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>
^E:\LAB\ROBOQUANT\SRC\EXPERIMENTS\DATAHUB_MODERN\BUILD\_DEPS\CATCH2-SUBBUILD\CMAKEFILES\A6D45BD166179965C26F64C43147948F\CATCH2-POPULATE-MKDIR.RULE
setlocal
"D:\Program Files\CMake\bin\cmake.exe" -Dcfgdir=/Debug -P E:/lab/RoboQuant/src/Experiments/DataHub_Modern/build/_deps/catch2-subbuild/catch2-populate-prefix/tmp/catch2-populate-mkdirs.cmake
if %errorlevel% neq 0 goto :cmEnd
"D:\Program Files\CMake\bin\cmake.exe" -E touch E:/lab/RoboQuant/src/Experiments/DataHub_Modern/build/_deps/catch2-subbuild/catch2-populate-prefix/src/catch2-populate-stamp/Debug/catch2-populate-mkdir
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^E:\LAB\ROBOQUANT\SRC\EXPERIMENTS\DATAHUB_MODERN\BUILD\_DEPS\CATCH2-SUBBUILD\CMAKEFILES\A6D45BD166179965C26F64C43147948F\CATCH2-POPULATE-DOWNLOAD.RULE
setlocal
cd E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps
if %errorlevel% neq 0 goto :cmEnd
E:
if %errorlevel% neq 0 goto :cmEnd
"D:\Program Files\CMake\bin\cmake.exe" -P E:/lab/RoboQuant/src/Experiments/DataHub_Modern/build/_deps/catch2-subbuild/catch2-populate-prefix/tmp/catch2-populate-gitclone.cmake
if %errorlevel% neq 0 goto :cmEnd
"D:\Program Files\CMake\bin\cmake.exe" -E touch E:/lab/RoboQuant/src/Experiments/DataHub_Modern/build/_deps/catch2-subbuild/catch2-populate-prefix/src/catch2-populate-stamp/Debug/catch2-populate-download
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^E:\LAB\ROBOQUANT\SRC\EXPERIMENTS\DATAHUB_MODERN\BUILD\_DEPS\CATCH2-SUBBUILD\CMAKEFILES\A6D45BD166179965C26F64C43147948F\CATCH2-POPULATE-UPDATE.RULE
setlocal
cd E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-src
if %errorlevel% neq 0 goto :cmEnd
E:
if %errorlevel% neq 0 goto :cmEnd
"D:\Program Files\CMake\bin\cmake.exe" -P E:/lab/RoboQuant/src/Experiments/DataHub_Modern/build/_deps/catch2-subbuild/catch2-populate-prefix/tmp/catch2-populate-gitupdate.cmake
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^E:\LAB\ROBOQUANT\SRC\EXPERIMENTS\DATAHUB_MODERN\BUILD\_DEPS\CATCH2-SUBBUILD\CMAKEFILES\A6D45BD166179965C26F64C43147948F\CATCH2-POPULATE-PATCH.RULE
setlocal
"D:\Program Files\CMake\bin\cmake.exe" -E echo_append
if %errorlevel% neq 0 goto :cmEnd
"D:\Program Files\CMake\bin\cmake.exe" -E touch E:/lab/RoboQuant/src/Experiments/DataHub_Modern/build/_deps/catch2-subbuild/catch2-populate-prefix/src/catch2-populate-stamp/Debug/catch2-populate-patch
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^E:\LAB\ROBOQUANT\SRC\EXPERIMENTS\DATAHUB_MODERN\BUILD\_DEPS\CATCH2-SUBBUILD\CMAKEFILES\A6D45BD166179965C26F64C43147948F\CATCH2-POPULATE-CONFIGURE.RULE
setlocal
cd E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-build
if %errorlevel% neq 0 goto :cmEnd
E:
if %errorlevel% neq 0 goto :cmEnd
"D:\Program Files\CMake\bin\cmake.exe" -E echo_append
if %errorlevel% neq 0 goto :cmEnd
"D:\Program Files\CMake\bin\cmake.exe" -E touch E:/lab/RoboQuant/src/Experiments/DataHub_Modern/build/_deps/catch2-subbuild/catch2-populate-prefix/src/catch2-populate-stamp/Debug/catch2-populate-configure
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^E:\LAB\ROBOQUANT\SRC\EXPERIMENTS\DATAHUB_MODERN\BUILD\_DEPS\CATCH2-SUBBUILD\CMAKEFILES\A6D45BD166179965C26F64C43147948F\CATCH2-POPULATE-BUILD.RULE
setlocal
cd E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-build
if %errorlevel% neq 0 goto :cmEnd
E:
if %errorlevel% neq 0 goto :cmEnd
"D:\Program Files\CMake\bin\cmake.exe" -E echo_append
if %errorlevel% neq 0 goto :cmEnd
"D:\Program Files\CMake\bin\cmake.exe" -E touch E:/lab/RoboQuant/src/Experiments/DataHub_Modern/build/_deps/catch2-subbuild/catch2-populate-prefix/src/catch2-populate-stamp/Debug/catch2-populate-build
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^E:\LAB\ROBOQUANT\SRC\EXPERIMENTS\DATAHUB_MODERN\BUILD\_DEPS\CATCH2-SUBBUILD\CMAKEFILES\A6D45BD166179965C26F64C43147948F\CATCH2-POPULATE-INSTALL.RULE
setlocal
cd E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-build
if %errorlevel% neq 0 goto :cmEnd
E:
if %errorlevel% neq 0 goto :cmEnd
"D:\Program Files\CMake\bin\cmake.exe" -E echo_append
if %errorlevel% neq 0 goto :cmEnd
"D:\Program Files\CMake\bin\cmake.exe" -E touch E:/lab/RoboQuant/src/Experiments/DataHub_Modern/build/_deps/catch2-subbuild/catch2-populate-prefix/src/catch2-populate-stamp/Debug/catch2-populate-install
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^E:\LAB\ROBOQUANT\SRC\EXPERIMENTS\DATAHUB_MODERN\BUILD\_DEPS\CATCH2-SUBBUILD\CMAKEFILES\A6D45BD166179965C26F64C43147948F\CATCH2-POPULATE-TEST.RULE
setlocal
cd E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-build
if %errorlevel% neq 0 goto :cmEnd
E:
if %errorlevel% neq 0 goto :cmEnd
"D:\Program Files\CMake\bin\cmake.exe" -E echo_append
if %errorlevel% neq 0 goto :cmEnd
"D:\Program Files\CMake\bin\cmake.exe" -E touch E:/lab/RoboQuant/src/Experiments/DataHub_Modern/build/_deps/catch2-subbuild/catch2-populate-prefix/src/catch2-populate-stamp/Debug/catch2-populate-test
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^E:\LAB\ROBOQUANT\SRC\EXPERIMENTS\DATAHUB_MODERN\BUILD\_DEPS\CATCH2-SUBBUILD\CMAKEFILES\B7B71D606AFD8B5D90C9DFD1A7387E4F\CATCH2-POPULATE-COMPLETE.RULE
setlocal
"D:\Program Files\CMake\bin\cmake.exe" -E make_directory E:/lab/RoboQuant/src/Experiments/DataHub_Modern/build/_deps/catch2-subbuild/CMakeFiles/Debug
if %errorlevel% neq 0 goto :cmEnd
"D:\Program Files\CMake\bin\cmake.exe" -E touch E:/lab/RoboQuant/src/Experiments/DataHub_Modern/build/_deps/catch2-subbuild/CMakeFiles/Debug/catch2-populate-complete
if %errorlevel% neq 0 goto :cmEnd
"D:\Program Files\CMake\bin\cmake.exe" -E touch E:/lab/RoboQuant/src/Experiments/DataHub_Modern/build/_deps/catch2-subbuild/catch2-populate-prefix/src/catch2-populate-stamp/Debug/catch2-populate-done
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^E:\LAB\ROBOQUANT\SRC\EXPERIMENTS\DATAHUB_MODERN\BUILD\_DEPS\CATCH2-SUBBUILD\CMAKEFILES\C2482A8FE812196CEE829A335FD55E97\CATCH2-POPULATE.RULE
setlocal
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^E:\LAB\ROBOQUANT\SRC\EXPERIMENTS\DATAHUB_MODERN\BUILD\_DEPS\CATCH2-SUBBUILD\CMAKELISTS.TXT
setlocal
"D:\Program Files\CMake\bin\cmake.exe" -SE:/lab/RoboQuant/src/Experiments/DataHub_Modern/build/_deps/catch2-subbuild -BE:/lab/RoboQuant/src/Experiments/DataHub_Modern/build/_deps/catch2-subbuild --check-stamp-file E:/lab/RoboQuant/src/Experiments/DataHub_Modern/build/_deps/catch2-subbuild/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd

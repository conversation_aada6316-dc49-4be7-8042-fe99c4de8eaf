/**
 * @file Algorithm.cpp
 * @brief Implementation of Algorithm classes
 * <AUTHOR> from Python implementation by <PERSON><PERSON><PERSON><PERSON> (<PERSON>)
 * @copyright Copyright 2019-2020, Heerozh. All rights reserved.
 * @license Apache 2.0
 */

#include "trading/Algorithm.h"
#include <algorithm>
#include <numeric>
#include <stdexcept>
#include <set>
#include <limits>

namespace Spectre {
namespace Trading {

// Recorder implementation
void Recorder::record(const TimePoint& date, const RecordMap& data) {
    if (data.find("date") != data.end()) {
        throw std::invalid_argument("'date' is reserved key for record");
    }
    
    records_.push_back({date, data});
}

std::unordered_map<std::string, torch::Tensor> Recorder::to_tensors() const {
    if (records_.empty()) {
        return {};
    }
    
    // Collect all unique keys
    std::set<std::string> all_keys;
    for (const auto& record : records_) {
        for (const auto& [key, value] : record.data) {
            all_keys.insert(key);
        }
    }
    
    std::unordered_map<std::string, torch::Tensor> result;
    
    for (const auto& key : all_keys) {
        std::vector<double> values;
        values.reserve(records_.size());
        
        for (const auto& record : records_) {
            auto it = record.data.find(key);
            if (it != record.data.end()) {
                values.push_back(it->second);
            } else {
                values.push_back(std::numeric_limits<double>::quiet_NaN());
            }
        }
        
        result[key] = torch::from_blob(values.data(), {static_cast<int64_t>(values.size())}, 
                                      torch::kDouble).clone();
    }
    
    return result;
}

std::vector<Recorder::TimePoint> Recorder::get_dates() const {
    std::vector<TimePoint> dates;
    dates.reserve(records_.size());
    
    for (const auto& record : records_) {
        dates.push_back(record.date);
    }
    
    return dates;
}

void Recorder::clear() {
    records_.clear();
}

// CustomAlgorithm implementation
CustomAlgorithm::CustomAlgorithm(std::shared_ptr<BaseBlotter> blotter)
    : blotter_(blotter), current_dt_(TimePoint{}), 
      history_window_(std::chrono::hours(0)), delay_factor_(true) {
    
    if (!blotter_) {
        throw std::invalid_argument("Blotter cannot be null");
    }
}

void CustomAlgorithm::clear() {
    recorder_.clear();
    current_dt_ = TimePoint{};
    current_data_.clear();
    results_ = AlgorithmResults{};
}

void CustomAlgorithm::set_datetime(const TimePoint& dt) {
    current_dt_ = dt;
    blotter_->set_datetime(dt);
}

void CustomAlgorithm::set_history_window(const Duration& window) {
    history_window_ = window;
}

void CustomAlgorithm::record(const std::unordered_map<std::string, double>& data) {
    recorder_.record(current_dt_, data);
}

void CustomAlgorithm::set_rebalance_callback(std::function<void()> callback) {
    rebalance_callback_ = std::move(callback);
}

AlgorithmResults CustomAlgorithm::get_results() {
    results_.returns = blotter_->get_returns();
    results_.positions = blotter_->get_historical_positions();
    results_.transactions = blotter_->get_transactions();
    results_.recorded_data = recorder_.to_tensors();
    
    return results_;
}

void CustomAlgorithm::on_run() {
    // Schedule events
    schedule(std::make_shared<MarketOpen>([this](EventReceiver* src) { on_market_open(src); }));
    schedule(std::make_shared<MarketClose>([this](EventReceiver* src) { on_market_close(src); }));
    schedule(std::make_shared<EveryBarData>([this](EventReceiver* src) { on_new_data(src); }));
    
    // Initialize algorithm
    initialize();
}

void CustomAlgorithm::on_end_of_run() {
    finalize();
}

void CustomAlgorithm::on_market_open(EventReceiver* source) {
    before_market_open();
}

void CustomAlgorithm::on_market_close(EventReceiver* source) {
    after_market_close();
}

void CustomAlgorithm::on_new_data(EventReceiver* source) {
    handle_data(current_data_);
}

void CustomAlgorithm::on_rebalance(EventReceiver* source) {
    if (rebalance_callback_) {
        rebalance_callback_();
    } else {
        rebalance();
    }
}

// BuyAndHoldAlgorithm implementation
BuyAndHoldAlgorithm::BuyAndHoldAlgorithm(std::shared_ptr<BaseBlotter> blotter,
                                       const std::vector<std::string>& assets,
                                       const std::vector<double>& weights)
    : CustomAlgorithm(blotter), assets_(assets), weights_(weights), initialized_(false) {
    
    if (assets_.size() != weights_.size()) {
        throw std::invalid_argument("Assets and weights must have the same size");
    }
    
    double sum = std::accumulate(weights_.begin(), weights_.end(), 0.0);
    if (std::abs(sum - 1.0) > 1e-6) {
        throw std::invalid_argument("Weights must sum to 1.0");
    }
}

void BuyAndHoldAlgorithm::initialize() {
    // Initial rebalance
    rebalance();
    initialized_ = true;
}

void BuyAndHoldAlgorithm::rebalance() {
    if (!initialized_) {
        // Initial purchase
        auto skipped = blotter_->batch_order_target_percent(assets_, weights_);
        
        // Record any skipped orders
        if (!skipped.empty()) {
            std::unordered_map<std::string, double> record_data;
            record_data["skipped_orders"] = static_cast<double>(skipped.size());
            record(record_data);
        }
    }
    // For buy-and-hold, we don't rebalance after initial purchase
}

// MomentumAlgorithm implementation
MomentumAlgorithm::MomentumAlgorithm(std::shared_ptr<BaseBlotter> blotter,
                                   int lookback_days, int top_n)
    : CustomAlgorithm(blotter), lookback_days_(lookback_days), top_n_(top_n) {
    
    if (lookback_days_ <= 0) {
        throw std::invalid_argument("Lookback days must be positive");
    }
    
    if (top_n_ <= 0) {
        throw std::invalid_argument("Top N must be positive");
    }
}

void MomentumAlgorithm::initialize() {
    // Nothing special to do for initialization
}

void MomentumAlgorithm::handle_data(const std::unordered_map<std::string, double>& data) {
    // Update price history
    for (const auto& [asset, price] : data) {
        auto& history = price_history_[asset];
        history.push_back(price);
        
        // Keep only the required lookback period
        if (static_cast<int>(history.size()) > lookback_days_) {
            history.erase(history.begin());
        }
    }
    
    current_data_ = data;
}

void MomentumAlgorithm::rebalance() {
    auto selected_assets = select_assets();
    
    if (selected_assets.empty()) {
        return; // No assets to trade
    }
    
    // Equal weight allocation
    double weight_per_asset = 1.0 / selected_assets.size();
    std::vector<double> weights(selected_assets.size(), weight_per_asset);
    
    // Close all existing positions first
    for (const auto& [asset, position] : blotter_->positions()) {
        if (std::find(selected_assets.begin(), selected_assets.end(), asset) == selected_assets.end()) {
            blotter_->order_target_percent(asset, 0.0);
        }
    }
    
    // Open new positions
    auto skipped = blotter_->batch_order_target_percent(selected_assets, weights);
    
    // Record momentum scores and selection
    std::unordered_map<std::string, double> record_data;
    record_data["selected_assets"] = static_cast<double>(selected_assets.size());
    record_data["skipped_orders"] = static_cast<double>(skipped.size());
    record(record_data);
}

double MomentumAlgorithm::calculate_momentum(const std::string& asset) const {
    auto it = price_history_.find(asset);
    if (it == price_history_.end() || static_cast<int>(it->second.size()) < lookback_days_) {
        return 0.0; // Not enough data
    }
    
    const auto& prices = it->second;
    double start_price = prices[0];
    double end_price = prices.back();
    
    if (start_price <= 0.0) {
        return 0.0;
    }
    
    return (end_price / start_price) - 1.0; // Return over lookback period
}

std::vector<std::string> MomentumAlgorithm::select_assets() const {
    std::vector<std::pair<std::string, double>> momentum_scores;
    
    for (const auto& [asset, history] : price_history_) {
        if (static_cast<int>(history.size()) >= lookback_days_) {
            double momentum = calculate_momentum(asset);
            momentum_scores.emplace_back(asset, momentum);
        }
    }
    
    // Sort by momentum (descending)
    std::sort(momentum_scores.begin(), momentum_scores.end(),
              [](const auto& a, const auto& b) {
                  return a.second > b.second;
              });
    
    // Select top N
    std::vector<std::string> selected;
    int count = std::min(top_n_, static_cast<int>(momentum_scores.size()));
    
    for (int i = 0; i < count; ++i) {
        if (momentum_scores[i].second > 0.0) { // Only positive momentum
            selected.push_back(momentum_scores[i].first);
        }
    }
    
    return selected;
}

} // namespace Trading
} // namespace Spectre

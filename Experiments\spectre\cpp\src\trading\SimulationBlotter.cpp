/**
 * @file SimulationBlotter.cpp
 * @brief Implementation of SimulationBlotter class
 * <AUTHOR> from Python implementation by <PERSON><PERSON><PERSON><PERSON> (<PERSON>)
 * @copyright Copyright 2019-2020, Heerozh. All rights reserved.
 * @license Apache 2.0
 */

#include "trading/SimulationBlotter.h"
#include <limits>
#include <stdexcept>
#include <cmath>
#include <algorithm>

namespace Spectre {
namespace Trading {

SimulationBlotter::SimulationBlotter(double capital_base, std::shared_ptr<DailyCurbModel> daily_curb)
    : BaseBlotter(), market_opened_(false), capital_base_(capital_base), 
      daily_curb_(daily_curb), current_price_source_(PriceSource::CLOSE) {
    
    portfolio_.update_cash(capital_base, true);
}

void SimulationBlotter::clear() {
    orders_.clear();
    portfolio_.clear();
    portfolio_.update_cash(capital_base_, true);
    market_opened_ = false;
}

void SimulationBlotter::set_datetime(const TimePoint& dt) {
    // Process borrow interest if there's a previous datetime
    if (current_dt_ != TimePoint{}) {
        auto duration = std::chrono::duration_cast<std::chrono::hours>(dt - current_dt_);
        double days_passed = duration.count() / 24.0;
        
        if (days_passed > 0) {
            portfolio_.process_borrow_interest(days_passed, 
                                             borrow_money_interest_rate_, 
                                             borrow_stock_interest_rate_);
        }
    }
    
    BaseBlotter::set_datetime(dt);
    
    // Clear current market data for new datetime
    current_prices_.clear();
    current_volumes_.clear();
    current_ohlc_.clear();
    previous_day_data_.clear();
}

void SimulationBlotter::set_price(const std::string& name) {
    if (name == "open") {
        current_price_source_ = PriceSource::OPEN;
    } else if (name == "close") {
        current_price_source_ = PriceSource::CLOSE;
    } else {
        throw std::invalid_argument("Price source must be 'open' or 'close'");
    }
}

void SimulationBlotter::set_current_prices(const std::unordered_map<AssetId, double>& prices) {
    current_prices_ = prices;
}

void SimulationBlotter::set_current_volumes(const std::unordered_map<AssetId, double>& volumes) {
    current_volumes_ = volumes;
}

void SimulationBlotter::set_current_ohlc(const std::unordered_map<AssetId, std::vector<double>>& ohlc_data) {
    current_ohlc_ = ohlc_data;
}

void SimulationBlotter::set_previous_day_data(const std::unordered_map<AssetId, std::vector<double>>& prev_data) {
    previous_day_data_ = prev_data;
}

double SimulationBlotter::get_price(const AssetId& asset) {
    check_market_hours();
    
    auto it = current_prices_.find(asset);
    if (it != current_prices_.end()) {
        return it->second;
    }
    
    // Try to get price from OHLC data
    auto ohlc_it = current_ohlc_.find(asset);
    if (ohlc_it != current_ohlc_.end() && ohlc_it->second.size() >= 4) {
        const auto& ohlc = ohlc_it->second;
        switch (current_price_source_) {
            case PriceSource::OPEN:
                return ohlc[0]; // Open
            case PriceSource::CLOSE:
                return ohlc[3]; // Close
        }
    }
    
    return std::numeric_limits<double>::quiet_NaN();
}

void SimulationBlotter::cancel_all_orders() {
    // In simulation, orders are executed immediately, so nothing to cancel
}

std::vector<Order> SimulationBlotter::get_transactions() const {
    std::vector<Order> transactions;
    
    for (const auto& [asset, asset_orders] : orders_) {
        for (const auto& order : asset_orders) {
            transactions.push_back(order);
        }
    }
    
    // Sort by date
    std::sort(transactions.begin(), transactions.end(),
              [](const Order& a, const Order& b) {
                  return a.date < b.date;
              });
    
    return transactions;
}

void SimulationBlotter::update_portfolio_value() {
    if (!portfolio_.positions().empty()) {
        portfolio_.update_value(current_prices_);
    }
}

void SimulationBlotter::process_dividend(const AssetId& asset, double amount, double tax) {
    double net_dividend = amount - div_tax_.calculate(asset, amount, 1);
    portfolio_.process_dividend(asset, net_dividend, tax);
}

void SimulationBlotter::process_split(const AssetId& asset, double inverse_ratio, double last_price) {
    portfolio_.process_split(asset, inverse_ratio, last_price);
}

void SimulationBlotter::market_open(EventReceiver* source) {
    market_opened_ = true;
}

void SimulationBlotter::market_close(EventReceiver* source) {
    cancel_all_orders();
    market_opened_ = false;
}

void SimulationBlotter::new_bars_data(EventReceiver* source) {
    if (market_opened_) {
        update_portfolio_value();
    }
}

void SimulationBlotter::on_run() {
    schedule(std::make_shared<MarketOpen>([this](EventReceiver* src) { market_open(src); }, -1));
    schedule(std::make_shared<MarketClose>([this](EventReceiver* src) { market_close(src); }));
    schedule(std::make_shared<EveryBarData>([this](EventReceiver* src) { new_bars_data(src); }));
}

bool SimulationBlotter::_order(const AssetId& asset, int64_t amount, double* price) {
    check_market_hours();
    
    if (amount == 0) {
        return true;
    }
    
    // Get current price
    double current_price = get_price(asset);
    if (std::isnan(current_price) || current_price <= 0.0) {
        return false;
    }
    
    // Check daily curb if enabled
    if (daily_curb_) {
        auto ohlc_it = current_ohlc_.find(asset);
        auto prev_it = previous_day_data_.find(asset);
        
        if (ohlc_it != current_ohlc_.end() && prev_it != previous_day_data_.end() &&
            ohlc_it->second.size() >= 4 && prev_it->second.size() >= 3) {
            
            const auto& ohlc = ohlc_it->second;
            const auto& prev_data = prev_it->second;
            
            auto [adjusted_price, allowed_amount] = daily_curb_->calculate(
                asset, current_price, amount,
                ohlc[1], ohlc[2], // high, low
                prev_data[0], prev_data[1], prev_data[2] // close, div, split
            );
            
            if (allowed_amount == 0) {
                return false; // Trading halted
            }
            
            amount = allowed_amount;
            current_price = adjusted_price;
        }
    }
    
    // Apply slippage if enabled
    double fill_price = current_price;
    if (!slippage_.is_empty()) {
        auto vol_it = current_volumes_.find(asset);
        if (vol_it != current_volumes_.end() && vol_it->second > 0) {
            double amount_to_volume_ratio = std::abs(amount) / vol_it->second;
            fill_price = slippage_.calculate_price(asset, current_price, amount_to_volume_ratio);
        }
    }
    
    // Calculate commission
    double commission = commission_.calculate(asset, fill_price, amount);
    if (amount < 0) { // Short selling
        commission += short_fee_.calculate(asset, fill_price, amount);
    }
    commission = std::round(commission * 100.0) / 100.0; // Round to 2 decimal places
    
    // Update portfolio
    double realized = portfolio_.update(asset, amount, fill_price, commission);
    portfolio_.update_cash(-amount * fill_price - commission);
    
    // Create order record
    Order order(current_dt_, asset, amount, current_price, fill_price, commission, realized);
    orders_[asset].push_back(order);
    
    return true;
}

const std::unordered_map<SimulationBlotter::AssetId, double>& 
SimulationBlotter::get_current_prices() const {
    return current_prices_;
}

void SimulationBlotter::check_market_hours() const {
    if (!market_opened_) {
        throw std::runtime_error(
            "Out of market hours. Maybe you rebalance at AfterMarketClose; "
            "or BeforeMarketOpen; or EveryBarData on daily data; "
            "or you did not subscribe this class with EventManager");
    }
}

} // namespace Trading
} // namespace Spectre

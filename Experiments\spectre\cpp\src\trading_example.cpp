/**
 * @file trading_example.cpp
 * @brief Complete example demonstrating the Spectre Trading Module
 * <AUTHOR> from Python implementation by <PERSON><PERSON><PERSON><PERSON> (<PERSON>)
 * @copyright Copyright 2019-2020, He<PERSON>zh. All rights reserved.
 * @license Apache 2.0
 */

#include "trading/Trading.h"
#include <iostream>
#include <iomanip>
#include <chrono>
#include <random>

using namespace Spectre::Trading;

/**
 * @brief Custom momentum strategy example
 */
class CustomMomentumStrategy : public CustomAlgorithm {
public:
    CustomMomentumStrategy(std::shared_ptr<BaseBlotter> blotter, 
                          const std::vector<std::string>& universe,
                          int lookback_days = 20, int top_n = 5)
        : CustomAlgorithm(blotter), universe_(universe), 
          lookback_days_(lookback_days), top_n_(top_n), rebalance_count_(0) {}

    void initialize() override {
        std::cout << "Initializing Custom Momentum Strategy..." << std::endl;
        std::cout << "Universe size: " << universe_.size() << std::endl;
        std::cout << "Lookback days: " << lookback_days_ << std::endl;
        std::cout << "Top N assets: " << top_n_ << std::endl;
    }

    void handle_data(const std::unordered_map<std::string, double>& data) override {
        // Update price history
        for (const auto& [asset, price] : data) {
            if (std::find(universe_.begin(), universe_.end(), asset) != universe_.end()) {
                auto& history = price_history_[asset];
                history.push_back(price);
                
                // Keep only the required lookback period
                if (static_cast<int>(history.size()) > lookback_days_) {
                    history.erase(history.begin());
                }
            }
        }
        
        // Record portfolio metrics
        double portfolio_value = blotter_->portfolio().value();
        double cash = blotter_->portfolio().cash();
        
        record({
            {"portfolio_value", portfolio_value},
            {"cash", cash},
            {"num_positions", static_cast<double>(blotter_->positions().size())}
        });
    }

    void rebalance() override {
        rebalance_count_++;
        std::cout << "\n--- Rebalancing #" << rebalance_count_ << " ---" << std::endl;
        
        auto selected_assets = select_top_momentum_assets();
        
        if (selected_assets.empty()) {
            std::cout << "No assets selected for trading" << std::endl;
            return;
        }
        
        std::cout << "Selected assets: ";
        for (const auto& asset : selected_assets) {
            std::cout << asset << " ";
        }
        std::cout << std::endl;
        
        // Equal weight allocation
        double weight_per_asset = 1.0 / selected_assets.size();
        std::vector<double> weights(selected_assets.size(), weight_per_asset);
        
        // Close positions not in selected assets
        for (const auto& [asset, position] : blotter_->positions()) {
            if (std::find(selected_assets.begin(), selected_assets.end(), asset) == selected_assets.end()) {
                blotter_->order_target_percent(asset, 0.0);
                std::cout << "Closed position in " << asset << std::endl;
            }
        }
        
        // Open new positions
        auto skipped = blotter_->batch_order_target_percent(selected_assets, weights);
        
        if (!skipped.empty()) {
            std::cout << "Skipped orders: ";
            for (const auto& [asset, shares] : skipped) {
                std::cout << asset << "(" << shares << ") ";
            }
            std::cout << std::endl;
        }
        
        // Print current positions
        std::cout << "Current positions:" << std::endl;
        for (const auto& [asset, position] : blotter_->positions()) {
            std::cout << "  " << asset << ": " << position.shares() 
                     << " shares @ $" << std::fixed << std::setprecision(2) 
                     << position.average_price() << std::endl;
        }
        
        std::cout << "Portfolio value: $" << std::fixed << std::setprecision(2) 
                 << blotter_->portfolio().value() << std::endl;
    }

private:
    std::vector<std::string> universe_;
    int lookback_days_;
    int top_n_;
    int rebalance_count_;
    std::unordered_map<std::string, std::vector<double>> price_history_;
    
    double calculate_momentum(const std::string& asset) const {
        auto it = price_history_.find(asset);
        if (it == price_history_.end() || static_cast<int>(it->second.size()) < lookback_days_) {
            return -1.0; // Not enough data
        }
        
        const auto& prices = it->second;
        double start_price = prices[0];
        double end_price = prices.back();
        
        if (start_price <= 0.0) {
            return -1.0;
        }
        
        return (end_price / start_price) - 1.0; // Return over lookback period
    }
    
    std::vector<std::string> select_top_momentum_assets() const {
        std::vector<std::pair<std::string, double>> momentum_scores;
        
        for (const auto& asset : universe_) {
            double momentum = calculate_momentum(asset);
            if (momentum > 0.0) { // Only positive momentum
                momentum_scores.emplace_back(asset, momentum);
            }
        }
        
        // Sort by momentum (descending)
        std::sort(momentum_scores.begin(), momentum_scores.end(),
                  [](const auto& a, const auto& b) {
                      return a.second > b.second;
                  });
        
        // Select top N
        std::vector<std::string> selected;
        int count = std::min(top_n_, static_cast<int>(momentum_scores.size()));
        
        for (int i = 0; i < count; ++i) {
            selected.push_back(momentum_scores[i].first);
        }
        
        return selected;
    }
};

/**
 * @brief Generate synthetic price data for testing
 */
std::unordered_map<std::string, std::vector<std::pair<std::chrono::system_clock::time_point, double>>>
generate_synthetic_data(const std::vector<std::string>& assets, int num_days) {
    std::unordered_map<std::string, std::vector<std::pair<std::chrono::system_clock::time_point, double>>> data;
    
    std::random_device rd;
    std::mt19937 gen(rd());
    std::normal_distribution<> price_change(0.001, 0.02); // 0.1% daily return, 2% volatility
    
    auto start_date = std::chrono::system_clock::now() - std::chrono::hours(24 * num_days);
    
    for (const auto& asset : assets) {
        double price = 100.0; // Starting price
        
        for (int day = 0; day < num_days; ++day) {
            auto date = start_date + std::chrono::hours(24 * day);
            
            // Apply random price change
            double change = price_change(gen);
            price *= (1.0 + change);
            
            data[asset].emplace_back(date, price);
        }
    }
    
    return data;
}

/**
 * @brief Run a simple backtest simulation
 */
void run_backtest_example() {
    std::cout << "=== Spectre Trading Module - Complete Example ===" << std::endl;
    
    // Initialize trading module
    Spectre::Trading::initialize();
    
    // Create simulation blotter with $100,000 initial capital
    auto blotter = std::make_shared<SimulationBlotter>(100000.0);
    
    // Set commission and slippage
    blotter->set_commission(0.001, 0.0, 1.0); // 0.1% + $1 minimum
    blotter->set_slippage(0.0005, 0.0002); // 0.05% max slippage
    
    // Define universe of assets
    std::vector<std::string> universe = {"AAPL", "GOOGL", "MSFT", "AMZN", "TSLA", "META", "NVDA", "NFLX"};
    
    // Create custom momentum strategy
    auto strategy = std::make_shared<CustomMomentumStrategy>(blotter, universe, 20, 3);
    
    // Generate synthetic price data for 100 days
    auto price_data = generate_synthetic_data(universe, 100);
    
    std::cout << "\nGenerated " << price_data.size() << " assets with " 
              << price_data.begin()->second.size() << " days of data" << std::endl;
    
    // Initialize strategy
    strategy->initialize();
    
    // Simulate trading over time
    int rebalance_frequency = 10; // Rebalance every 10 days
    int day_count = 0;
    
    for (int day = 0; day < 100; ++day) {
        // Get current date
        auto current_date = price_data.begin()->second[day].first;
        
        // Set current datetime
        blotter->set_datetime(current_date);
        strategy->set_datetime(current_date);
        
        // Prepare current market data
        std::unordered_map<std::string, double> current_prices;
        for (const auto& [asset, price_series] : price_data) {
            current_prices[asset] = price_series[day].second;
        }
        
        // Set market data in blotter
        blotter->set_current_prices(current_prices);
        
        // Open market
        blotter->market_open(nullptr);
        
        // Handle data
        strategy->handle_data(current_prices);
        
        // Rebalance periodically
        if (day % rebalance_frequency == 0) {
            strategy->rebalance();
        }
        
        // Update portfolio value
        blotter->update_portfolio_value();
        
        // Close market
        blotter->market_close(nullptr);
        
        day_count++;
    }
    
    // Get final results
    auto results = strategy->get_results();
    
    std::cout << "\n=== Final Results ===" << std::endl;
    std::cout << "Total days simulated: " << day_count << std::endl;
    std::cout << "Final portfolio value: $" << std::fixed << std::setprecision(2) 
              << blotter->portfolio().value() << std::endl;
    std::cout << "Total return: " << std::fixed << std::setprecision(2) 
              << ((blotter->portfolio().value() / 100000.0 - 1.0) * 100.0) << "%" << std::endl;
    
    // Print transaction summary
    auto transactions = results.transactions;
    std::cout << "Total transactions: " << transactions.size() << std::endl;
    
    if (!transactions.empty()) {
        double total_commission = 0.0;
        for (const auto& txn : transactions) {
            total_commission += txn.commission;
        }
        std::cout << "Total commission paid: $" << std::fixed << std::setprecision(2) 
                  << total_commission << std::endl;
    }
    
    // Calculate and display performance metrics
    if (results.returns.numel() > 1) {
        double sharpe = Metric::sharpe_ratio(results.returns, 0.02);
        double volatility = Metric::annual_volatility(results.returns);
        double max_dd = Metric::max_drawdown(results.returns);
        
        std::cout << "\n=== Performance Metrics ===" << std::endl;
        std::cout << "Sharpe Ratio: " << std::fixed << std::setprecision(4) << sharpe << std::endl;
        std::cout << "Annual Volatility: " << std::fixed << std::setprecision(2) 
                  << (volatility * 100.0) << "%" << std::endl;
        std::cout << "Maximum Drawdown: " << std::fixed << std::setprecision(2) 
                  << (max_dd * 100.0) << "%" << std::endl;
    }
    
    // Cleanup
    Spectre::Trading::cleanup();
    
    std::cout << "\nBacktest completed successfully!" << std::endl;
}

int main() {
    try {
        run_backtest_example();
        return 0;
    }
    catch (const std::exception& e) {
        std::cerr << "Error: " << e.what() << std::endl;
        return 1;
    }
}

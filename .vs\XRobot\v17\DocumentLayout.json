{"Version": 1, "WorkspaceRootPath": "E:\\lab\\RoboQuant\\src\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{380CB2F2-8A5D-4652-877B-3D4DCA97717E}|DataHub\\DataHub.vcxproj|E:\\lab\\RoboQuant\\src\\DataHub\\Settings.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}", "RelativeMoniker": "D:0:0:{380CB2F2-8A5D-4652-877B-3D4DCA97717E}|DataHub\\DataHub.vcxproj|solutionrelative:DataHub\\Settings.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{02EA681E-C7D8-13C7-8484-4AC65E1B71E8}|docs{02EA681E-C7D8-13C7-8484-4AC65E1B71E8}|E:\\lab\\RoboQuant\\src\\readme.md||{EFC0BB08-EA7D-40C6-A696-C870411A895B}", "RelativeMoniker": "D:0:0:{02EA681E-C7D8-13C7-8484-4AC65E1B71E8}|docs{02EA681E-C7D8-13C7-8484-4AC65E1B71E8}|solutionrelative:readme.md||{EFC0BB08-EA7D-40C6-A696-C870411A895B}"}, {"AbsoluteMoniker": "D:0:0:{380CB2F2-8A5D-4652-877B-3D4DCA97717E}|DataHub\\DataHub.vcxproj|E:\\lab\\RoboQuant\\src\\DataHub\\DataHubImpl.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}", "RelativeMoniker": "D:0:0:{380CB2F2-8A5D-4652-877B-3D4DCA97717E}|DataHub\\DataHub.vcxproj|solutionrelative:DataHub\\DataHubImpl.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{380CB2F2-8A5D-4652-877B-3D4DCA97717E}|DataHub\\DataHub.vcxproj|E:\\lab\\RoboQuant\\src\\DataHub\\DataHubImpl.h||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}", "RelativeMoniker": "D:0:0:{380CB2F2-8A5D-4652-877B-3D4DCA97717E}|DataHub\\DataHub.vcxproj|solutionrelative:DataHub\\DataHubImpl.h||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{14506C7D-8771-41CE-BFC8-62E94B95E941}|XRobot\\XRobot.vcxproj|E:\\lab\\RoboQuant\\src\\XRobot\\DlgOrderManagement.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}", "RelativeMoniker": "D:0:0:{14506C7D-8771-41CE-BFC8-62E94B95E941}|XRobot\\XRobot.vcxproj|solutionrelative:XRobot\\DlgOrderManagement.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{A23A9337-4BCD-4E83-8B6A-5B17BB09E970}|TradingSvr\\TradingSvr.vcxproj|E:\\lab\\RoboQuant\\src\\TradingSvr\\FuturesStrategy.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}", "RelativeMoniker": "D:0:0:{A23A9337-4BCD-4E83-8B6A-5B17BB09E970}|TradingSvr\\TradingSvr.vcxproj|solutionrelative:TradingSvr\\FuturesStrategy.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{A23A9337-4BCD-4E83-8B6A-5B17BB09E970}|TradingSvr\\TradingSvr.vcxproj|E:\\lab\\RoboQuant\\src\\TradingSvr\\TradingSvr.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}", "RelativeMoniker": "D:0:0:{A23A9337-4BCD-4E83-8B6A-5B17BB09E970}|TradingSvr\\TradingSvr.vcxproj|solutionrelative:TradingSvr\\TradingSvr.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{A23A9337-4BCD-4E83-8B6A-5B17BB09E970}|TradingSvr\\TradingSvr.vcxproj|E:\\lab\\RoboQuant\\src\\TradingSvr\\ModelHelper.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}", "RelativeMoniker": "D:0:0:{A23A9337-4BCD-4E83-8B6A-5B17BB09E970}|TradingSvr\\TradingSvr.vcxproj|solutionrelative:TradingSvr\\ModelHelper.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{380CB2F2-8A5D-4652-877B-3D4DCA97717E}|DataHub\\DataHub.vcxproj|E:\\lab\\RoboQuant\\src\\DataHub\\DataManager.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}", "RelativeMoniker": "D:0:0:{380CB2F2-8A5D-4652-877B-3D4DCA97717E}|DataHub\\DataHub.vcxproj|solutionrelative:DataHub\\DataManager.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{380CB2F2-8A5D-4652-877B-3D4DCA97717E}|DataHub\\DataHub.vcxproj|E:\\lab\\RoboQuant\\src\\DataHub\\InstrumentImpl.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}", "RelativeMoniker": "D:0:0:{380CB2F2-8A5D-4652-877B-3D4DCA97717E}|DataHub\\DataHub.vcxproj|solutionrelative:DataHub\\InstrumentImpl.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{A23A9337-4BCD-4E83-8B6A-5B17BB09E970}|TradingSvr\\TradingSvr.vcxproj|E:\\lab\\RoboQuant\\src\\TradingSvr\\PortfolioI.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}", "RelativeMoniker": "D:0:0:{A23A9337-4BCD-4E83-8B6A-5B17BB09E970}|TradingSvr\\TradingSvr.vcxproj|solutionrelative:TradingSvr\\PortfolioI.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{A23A9337-4BCD-4E83-8B6A-5B17BB09E970}|TradingSvr\\TradingSvr.vcxproj|E:\\lab\\RoboQuant\\src\\TradingSvr\\ExpressionParser.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}", "RelativeMoniker": "D:0:0:{A23A9337-4BCD-4E83-8B6A-5B17BB09E970}|TradingSvr\\TradingSvr.vcxproj|solutionrelative:TradingSvr\\ExpressionParser.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{380CB2F2-8A5D-4652-877B-3D4DCA97717E}|DataHub\\DataHub.vcxproj|E:\\lab\\RoboQuant\\src\\DataHub\\InstrumentImpl.h||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}", "RelativeMoniker": "D:0:0:{380CB2F2-8A5D-4652-877B-3D4DCA97717E}|DataHub\\DataHub.vcxproj|solutionrelative:DataHub\\InstrumentImpl.h||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{14506C7D-8771-41CE-BFC8-62E94B95E941}|XRobot\\XRobot.vcxproj|E:\\lab\\RoboQuant\\src\\XRobot\\DlgSimulation.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}", "RelativeMoniker": "D:0:0:{14506C7D-8771-41CE-BFC8-62E94B95E941}|XRobot\\XRobot.vcxproj|solutionrelative:XRobot\\DlgSimulation.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 1, "Children": [{"$type": "Document", "DocumentIndex": 1, "Title": "readme.md", "DocumentMoniker": "E:\\lab\\RoboQuant\\src\\readme.md", "RelativeDocumentMoniker": "readme.md", "ToolTip": "E:\\lab\\RoboQuant\\src\\readme.md", "RelativeToolTip": "readme.md", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAgAAAARAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001818|", "WhenOpened": "2025-06-27T01:24:12.083Z", "IsPinned": true, "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 0, "Title": "Settings.cpp", "DocumentMoniker": "E:\\lab\\RoboQuant\\src\\DataHub\\Settings.cpp", "RelativeDocumentMoniker": "DataHub\\Settings.cpp", "ToolTip": "E:\\lab\\RoboQuant\\src\\DataHub\\Settings.cpp", "RelativeToolTip": "DataHub\\Settings.cpp", "ViewState": "AgIAAAQBAAAAAAAAAAArwBYBAAADAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000677|", "WhenOpened": "2025-07-03T01:30:25.361Z", "EditorCaption": ""}, {"$type": "Bookmark", "Name": "ST:0:0:{3ae79031-e1bc-11d0-8f78-00a0c9110057}"}, {"$type": "Bookmark", "Name": "ST:0:0:{de1fc918-f32e-4dd7-a915-1792a051f26b}"}, {"$type": "Bookmark", "Name": "ST:0:0:{1c64b9c2-e352-428e-a56d-0ace190b99a6}"}, {"$type": "Bookmark", "Name": "ST:0:0:{aa2115a1-9712-457b-9047-dbb71ca2cdd2}"}, {"$type": "Document", "DocumentIndex": 7, "Title": "ModelHelper.cpp", "DocumentMoniker": "E:\\lab\\RoboQuant\\src\\TradingSvr\\ModelHelper.cpp", "RelativeDocumentMoniker": "TradingSvr\\ModelHelper.cpp", "ToolTip": "E:\\lab\\RoboQuant\\src\\TradingSvr\\ModelHelper.cpp", "RelativeToolTip": "TradingSvr\\ModelHelper.cpp", "ViewState": "AgIAAAcDAAAAAAAAAAAxwBkDAAADAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000677|", "WhenOpened": "2025-06-26T06:25:28.081Z"}, {"$type": "Document", "DocumentIndex": 9, "Title": "InstrumentImpl.cpp", "DocumentMoniker": "E:\\lab\\RoboQuant\\src\\DataHub\\InstrumentImpl.cpp", "RelativeDocumentMoniker": "DataHub\\InstrumentImpl.cpp", "ToolTip": "E:\\lab\\RoboQuant\\src\\DataHub\\InstrumentImpl.cpp", "RelativeToolTip": "DataHub\\InstrumentImpl.cpp", "ViewState": "AgIAAEUAAAAAAAAAAAAIwFMAAAAkAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000677|", "WhenOpened": "2024-12-03T12:21:38.781Z"}, {"$type": "Document", "DocumentIndex": 6, "Title": "TradingSvr.cpp", "DocumentMoniker": "E:\\lab\\RoboQuant\\src\\TradingSvr\\TradingSvr.cpp", "RelativeDocumentMoniker": "TradingSvr\\TradingSvr.cpp", "ToolTip": "E:\\lab\\RoboQuant\\src\\TradingSvr\\TradingSvr.cpp", "RelativeToolTip": "TradingSvr\\TradingSvr.cpp", "ViewState": "AgIAAGYBAAAAAAAAAAAxwJQBAAAcAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000677|", "WhenOpened": "2025-06-16T09:17:22.398Z"}, {"$type": "Document", "DocumentIndex": 8, "Title": "DataManager.cpp", "DocumentMoniker": "E:\\lab\\RoboQuant\\src\\DataHub\\DataManager.cpp", "RelativeDocumentMoniker": "DataHub\\DataManager.cpp", "ToolTip": "E:\\lab\\RoboQuant\\src\\DataHub\\DataManager.cpp", "RelativeToolTip": "DataHub\\DataManager.cpp", "ViewState": "AgIAADwBAAAAAAAAAAAMwFQBAAA4AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000677|", "WhenOpened": "2025-05-21T02:05:37.199Z"}, {"$type": "Document", "DocumentIndex": 10, "Title": "PortfolioI.cpp", "DocumentMoniker": "E:\\lab\\RoboQuant\\src\\TradingSvr\\PortfolioI.cpp", "RelativeDocumentMoniker": "TradingSvr\\PortfolioI.cpp", "ToolTip": "E:\\lab\\RoboQuant\\src\\TradingSvr\\PortfolioI.cpp", "RelativeToolTip": "TradingSvr\\PortfolioI.cpp", "ViewState": "AgIAAMwHAAAAAAAAAAAMwNsHAAALAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000677|", "WhenOpened": "2025-06-25T02:48:41.159Z"}, {"$type": "Document", "DocumentIndex": 4, "Title": "DlgOrderManagement.cpp", "DocumentMoniker": "E:\\lab\\RoboQuant\\src\\XRobot\\DlgOrderManagement.cpp", "RelativeDocumentMoniker": "XRobot\\DlgOrderManagement.cpp", "ToolTip": "E:\\lab\\RoboQuant\\src\\XRobot\\DlgOrderManagement.cpp", "RelativeToolTip": "XRobot\\DlgOrderManagement.cpp", "ViewState": "AgIAAL0EAAAAAAAAAAAgwMcEAAAmAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000677|", "WhenOpened": "2025-06-16T15:06:35.93Z"}, {"$type": "Document", "DocumentIndex": 11, "Title": "ExpressionParser.cpp", "DocumentMoniker": "E:\\lab\\RoboQuant\\src\\TradingSvr\\ExpressionParser.cpp", "RelativeDocumentMoniker": "TradingSvr\\ExpressionParser.cpp", "ToolTip": "E:\\lab\\RoboQuant\\src\\TradingSvr\\ExpressionParser.cpp", "RelativeToolTip": "TradingSvr\\ExpressionParser.cpp", "ViewState": "AgIAABQGAAAAAAAAAAAMwCAGAAAHAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000677|", "WhenOpened": "2025-06-16T14:25:42.539Z"}, {"$type": "Document", "DocumentIndex": 2, "Title": "DataHubImpl.cpp", "DocumentMoniker": "E:\\lab\\RoboQuant\\src\\DataHub\\DataHubImpl.cpp", "RelativeDocumentMoniker": "DataHub\\DataHubImpl.cpp", "ToolTip": "E:\\lab\\RoboQuant\\src\\DataHub\\DataHubImpl.cpp", "RelativeToolTip": "DataHub\\DataHubImpl.cpp", "ViewState": "AgIAAHAUAAAAAAAAAAAewHkUAAAhAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000677|", "WhenOpened": "2024-11-01T12:43:27.622Z"}, {"$type": "Document", "DocumentIndex": 3, "Title": "DataHubImpl.h", "DocumentMoniker": "E:\\lab\\RoboQuant\\src\\DataHub\\DataHubImpl.h", "RelativeDocumentMoniker": "DataHub\\DataHubImpl.h", "ToolTip": "E:\\lab\\RoboQuant\\src\\DataHub\\DataHubImpl.h*", "RelativeToolTip": "DataHub\\DataHubImpl.h*", "ViewState": "AgIAAFAAAAAAAAAAAAArwGMAAAAvAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000680|", "WhenOpened": "2025-06-04T08:18:46.528Z"}, {"$type": "Document", "DocumentIndex": 13, "Title": "DlgSimulation.cpp", "DocumentMoniker": "E:\\lab\\RoboQuant\\src\\XRobot\\DlgSimulation.cpp", "RelativeDocumentMoniker": "XRobot\\DlgSimulation.cpp", "ToolTip": "E:\\lab\\RoboQuant\\src\\XRobot\\DlgSimulation.cpp", "RelativeToolTip": "XRobot\\DlgSimulation.cpp", "ViewState": "AgIAALoAAAAAAAAAAAAowMcAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000677|", "WhenOpened": "2025-06-03T00:45:29.807Z"}, {"$type": "Document", "DocumentIndex": 12, "Title": "InstrumentImpl.h", "DocumentMoniker": "E:\\lab\\RoboQuant\\src\\DataHub\\InstrumentImpl.h", "RelativeDocumentMoniker": "DataHub\\InstrumentImpl.h", "ToolTip": "E:\\lab\\RoboQuant\\src\\DataHub\\InstrumentImpl.h", "RelativeToolTip": "DataHub\\InstrumentImpl.h", "ViewState": "AgIAABUAAAAAAAAAAAAAAC0AAAADAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000680|", "WhenOpened": "2025-04-24T02:48:14.125Z"}, {"$type": "Document", "DocumentIndex": 5, "Title": "FuturesStrategy.cpp", "DocumentMoniker": "E:\\lab\\RoboQuant\\src\\TradingSvr\\FuturesStrategy.cpp", "RelativeDocumentMoniker": "TradingSvr\\FuturesStrategy.cpp", "ToolTip": "E:\\lab\\RoboQuant\\src\\TradingSvr\\FuturesStrategy.cpp", "RelativeToolTip": "TradingSvr\\FuturesStrategy.cpp", "ViewState": "AgIAADsAAAAAAAAAAAAxwEUAAAARAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000677|", "WhenOpened": "2024-12-18T13:25:39.697Z"}]}]}]}
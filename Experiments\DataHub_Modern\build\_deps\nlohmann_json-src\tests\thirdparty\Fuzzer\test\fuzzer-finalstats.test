RUN: LLVMFuzzer-SimpleTest -seed=1 -runs=77 -print_final_stats=1 2>&1 | FileCheck %s --check-prefix=FINAL_STATS
FINAL_STATS: stat::number_of_executed_units: 77
FINAL_STATS: stat::average_exec_per_sec:     0
FINAL_STATS: stat::new_units_added:
FINAL_STATS: stat::slowest_unit_time_sec:    0
FINAL_STATS: stat::peak_rss_mb:

RUN: LLVMFuzzer-SimpleTest %S/dict1.txt -runs=33 -print_final_stats=1 2>&1 | FileCheck %s --check-prefix=FINAL_STATS1
FINAL_STATS1: stat::number_of_executed_units: 33
FINAL_STATS1: stat::peak_rss_mb:


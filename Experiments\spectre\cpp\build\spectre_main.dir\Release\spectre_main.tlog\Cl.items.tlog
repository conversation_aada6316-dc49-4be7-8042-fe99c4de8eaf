E:\lab\RoboQuant\src\Experiments\spectre\cpp\src\main.cpp;E:\lab\RoboQuant\src\Experiments\spectre\cpp\build\spectre_main.dir\Release\main.obj
E:\lab\RoboQuant\src\Experiments\spectre\cpp\src\Factor.cpp;E:\lab\RoboQuant\src\Experiments\spectre\cpp\build\spectre_main.dir\Release\Factor.obj
E:\lab\RoboQuant\src\Experiments\spectre\cpp\src\Engine.cpp;E:\lab\RoboQuant\src\Experiments\spectre\cpp\build\spectre_main.dir\Release\Engine.obj
E:\lab\RoboQuant\src\Experiments\spectre\cpp\src\Rolling.cpp;E:\lab\RoboQuant\src\Experiments\spectre\cpp\build\spectre_main.dir\Release\Rolling.obj
E:\lab\RoboQuant\src\Experiments\spectre\cpp\src\Parallel.cpp;E:\lab\RoboQuant\src\Experiments\spectre\cpp\build\spectre_main.dir\Release\Parallel.obj
E:\lab\RoboQuant\src\Experiments\spectre\cpp\src\RollingParallel.cpp;E:\lab\RoboQuant\src\Experiments\spectre\cpp\build\spectre_main.dir\Release\RollingParallel.obj
E:\lab\RoboQuant\src\Experiments\spectre\cpp\src\trading\Trading.cpp;E:\lab\RoboQuant\src\Experiments\spectre\cpp\build\spectre_main.dir\Release\Trading.obj
E:\lab\RoboQuant\src\Experiments\spectre\cpp\src\trading\Position.cpp;E:\lab\RoboQuant\src\Experiments\spectre\cpp\build\spectre_main.dir\Release\Position.obj
E:\lab\RoboQuant\src\Experiments\spectre\cpp\src\trading\Portfolio.cpp;E:\lab\RoboQuant\src\Experiments\spectre\cpp\build\spectre_main.dir\Release\Portfolio.obj
E:\lab\RoboQuant\src\Experiments\spectre\cpp\src\trading\Event.cpp;E:\lab\RoboQuant\src\Experiments\spectre\cpp\build\spectre_main.dir\Release\Event.obj
E:\lab\RoboQuant\src\Experiments\spectre\cpp\src\trading\StopModel.cpp;E:\lab\RoboQuant\src\Experiments\spectre\cpp\build\spectre_main.dir\Release\StopModel.obj
E:\lab\RoboQuant\src\Experiments\spectre\cpp\src\trading\Metric.cpp;E:\lab\RoboQuant\src\Experiments\spectre\cpp\build\spectre_main.dir\Release\Metric.obj
E:\lab\RoboQuant\src\Experiments\spectre\cpp\src\trading\Blotter.cpp;E:\lab\RoboQuant\src\Experiments\spectre\cpp\build\spectre_main.dir\Release\Blotter.obj
E:\lab\RoboQuant\src\Experiments\spectre\cpp\src\trading\SimulationBlotter.cpp;E:\lab\RoboQuant\src\Experiments\spectre\cpp\build\spectre_main.dir\Release\SimulationBlotter.obj
E:\lab\RoboQuant\src\Experiments\spectre\cpp\src\trading\Algorithm.cpp;E:\lab\RoboQuant\src\Experiments\spectre\cpp\build\spectre_main.dir\Release\Algorithm.obj

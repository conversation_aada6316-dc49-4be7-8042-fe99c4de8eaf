#pragma once

/**
 * @file Trading.h
 * @brief Main header file for the Spectre Trading module
 * <AUTHOR> from Python implementation by <PERSON><PERSON><PERSON><PERSON> (<PERSON>)
 * @copyright Copyright 2019-2020, He<PERSON>zh. All rights reserved.
 * @license Apache 2.0
 */

// Core trading components
#include "Position.h"
#include "Portfolio.h"
#include "Event.h"
#include "StopModel.h"
#include "Calendar.h"
#include "Metric.h"
#include "Blotter.h"
#include "SimulationBlotter.h"
#include "Algorithm.h"

// Utility includes
#include <torch/torch.h>
#include <memory>
#include <string>
#include <vector>
#include <unordered_map>
#include <chrono>

namespace Spectre {
namespace Trading {

/**
 * @brief Main namespace for all trading-related functionality
 * 
 * This namespace contains all classes and functions for:
 * - Position and portfolio management
 * - Event-driven trading system
 * - Order management and execution
 * - Trading algorithms and backtesting
 * - Performance metrics calculation
 */

// Forward declarations
class Position;
class Portfolio;
class EventManager;
class StopModel;

// Type aliases for convenience
using TimePoint = std::chrono::system_clock::time_point;
using Duration = std::chrono::duration<double>;
using AssetId = std::string;
using Price = double;
using Shares = int64_t;
using Amount = double;

// Common enums
enum class OrderType {
    Market,
    Limit,
    Stop,
    StopLimit
};

enum class OrderSide {
    Buy,
    Sell
};

enum class OrderStatus {
    Pending,
    Filled,
    PartiallyFilled,
    Cancelled,
    Rejected
};

/**
 * @brief Trading module version information
 */
constexpr const char* VERSION = "1.0.0";

/**
 * @brief Initialize the trading module
 */
void initialize();

/**
 * @brief Cleanup the trading module
 */
void cleanup();

} // namespace Trading
} // namespace Spectre

﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <CustomBuild Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-subbuild\CMakeFiles\a6d45bd166179965c26f64c43147948f\catch2-populate-mkdir.rule">
      <Filter>CMake Rules</Filter>
    </CustomBuild>
    <CustomBuild Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-subbuild\CMakeFiles\a6d45bd166179965c26f64c43147948f\catch2-populate-download.rule">
      <Filter>CMake Rules</Filter>
    </CustomBuild>
    <CustomBuild Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-subbuild\CMakeFiles\a6d45bd166179965c26f64c43147948f\catch2-populate-update.rule">
      <Filter>CMake Rules</Filter>
    </CustomBuild>
    <CustomBuild Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-subbuild\CMakeFiles\a6d45bd166179965c26f64c43147948f\catch2-populate-patch.rule">
      <Filter>CMake Rules</Filter>
    </CustomBuild>
    <CustomBuild Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-subbuild\CMakeFiles\a6d45bd166179965c26f64c43147948f\catch2-populate-configure.rule">
      <Filter>CMake Rules</Filter>
    </CustomBuild>
    <CustomBuild Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-subbuild\CMakeFiles\a6d45bd166179965c26f64c43147948f\catch2-populate-build.rule">
      <Filter>CMake Rules</Filter>
    </CustomBuild>
    <CustomBuild Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-subbuild\CMakeFiles\a6d45bd166179965c26f64c43147948f\catch2-populate-install.rule">
      <Filter>CMake Rules</Filter>
    </CustomBuild>
    <CustomBuild Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-subbuild\CMakeFiles\a6d45bd166179965c26f64c43147948f\catch2-populate-test.rule">
      <Filter>CMake Rules</Filter>
    </CustomBuild>
    <CustomBuild Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-subbuild\CMakeFiles\b7b71d606afd8b5d90c9dfd1a7387e4f\catch2-populate-complete.rule">
      <Filter>CMake Rules</Filter>
    </CustomBuild>
    <CustomBuild Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-subbuild\CMakeFiles\c2482a8fe812196cee829a335fd55e97\catch2-populate.rule">
      <Filter>CMake Rules</Filter>
    </CustomBuild>
    <CustomBuild Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-subbuild\CMakeLists.txt" />
  </ItemGroup>
  <ItemGroup>
    <None Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\catch2-subbuild\CMakeFiles\catch2-populate" />
  </ItemGroup>
  <ItemGroup>
    <Filter Include="CMake Rules">
      <UniqueIdentifier>{6A67B56F-FAAE-37CB-99A5-C2AB80BA5E39}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
</Project>

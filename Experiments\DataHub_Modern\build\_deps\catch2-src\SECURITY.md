# Security Policy

## Supported Versions

* Versions 1.x (branch Catch1.x) are no longer supported.
* Versions 2.x (branch v2.x) are currently supported.
* `devel` branch serves for stable-ish development and is supported,
  but branches `devel-*` are considered short lived and are not supported separately.


## Reporting a Vulnerability

Due to its nature as a _unit_ test framework, Catch2 shouldn't interact
with untrusted inputs and there shouldn't be many security vulnerabilities
in it.

However, if you find one you send email to martin <dot> ho<PERSON><PERSON> <at>
gmail <dot> com. If you want to encrypt the email, my pgp key is
`E29C 46F3 B8A7 5028 6079 3B7D ECC9 C20E 314B 2360`.

﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <PreferredToolArchitecture>x64</PreferredToolArchitecture>
  </PropertyGroup>
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="MinSizeRel|x64">
      <Configuration>MinSizeRel</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="RelWithDebInfo|x64">
      <Configuration>RelWithDebInfo</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{4F6C7749-5D04-3C41-A3C0-42468712FF5F}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <WindowsTargetPlatformVersion>10.0.26100.0</WindowsTargetPlatformVersion>
    <Platform>x64</Platform>
    <ProjectName>datahub_services</ProjectName>
    <VCProjectUpgraderObjectName>NoUpgrade</VCProjectUpgraderObjectName>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup>
    <_ProjectFileVersion>10.0.20506.1</_ProjectFileVersion>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\Debug\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">datahub_services.dir\Debug\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">datahub_services</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">.lib</TargetExt>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\Release\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">datahub_services.dir\Release\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Release|x64'">datahub_services</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Release|x64'">.lib</TargetExt>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\MinSizeRel\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">datahub_services.dir\MinSizeRel\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">datahub_services</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">.lib</TargetExt>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\RelWithDebInfo\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">datahub_services.dir\RelWithDebInfo\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">datahub_services</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">.lib</TargetExt>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>E:\lab\RoboQuant\src\Experiments\DataHub_Modern\include;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\spdlog-src\include;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\nlohmann_json-src\include;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\fmt-src\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /utf-8 /await</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <ExceptionHandling>Sync</ExceptionHandling>
      <InlineFunctionExpansion>Disabled</InlineFunctionExpansion>
      <LanguageStandard>stdcpp20</LanguageStandard>
      <Optimization>MaxSpeed</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
      <TreatWarningAsError>true</TreatWarningAsError>
      <UseFullPaths>false</UseFullPaths>
      <WarningLevel>Level4</WarningLevel>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;SPDLOG_COMPILED_LIB;CMAKE_INTDIR="Debug"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_DEBUG;_WINDOWS;NDEBUG;SPDLOG_COMPILED_LIB;CMAKE_INTDIR=\"Debug\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>E:\lab\RoboQuant\src\Experiments\DataHub_Modern\include;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\spdlog-src\include;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\nlohmann_json-src\include;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\fmt-src\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>E:\lab\RoboQuant\src\Experiments\DataHub_Modern\include;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\spdlog-src\include;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\nlohmann_json-src\include;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\fmt-src\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Lib>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
    </Lib>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>E:\lab\RoboQuant\src\Experiments\DataHub_Modern\include;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\spdlog-src\include;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\nlohmann_json-src\include;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\fmt-src\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /utf-8 /await</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <ExceptionHandling>Sync</ExceptionHandling>
      <InlineFunctionExpansion>AnySuitable</InlineFunctionExpansion>
      <LanguageStandard>stdcpp20</LanguageStandard>
      <Optimization>MaxSpeed</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <TreatWarningAsError>true</TreatWarningAsError>
      <UseFullPaths>false</UseFullPaths>
      <WarningLevel>Level4</WarningLevel>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;SPDLOG_COMPILED_LIB;CMAKE_INTDIR="Release"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <DebugInformationFormat>
      </DebugInformationFormat>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;SPDLOG_COMPILED_LIB;CMAKE_INTDIR=\"Release\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>E:\lab\RoboQuant\src\Experiments\DataHub_Modern\include;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\spdlog-src\include;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\nlohmann_json-src\include;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\fmt-src\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>E:\lab\RoboQuant\src\Experiments\DataHub_Modern\include;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\spdlog-src\include;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\nlohmann_json-src\include;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\fmt-src\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Lib>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
    </Lib>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>E:\lab\RoboQuant\src\Experiments\DataHub_Modern\include;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\spdlog-src\include;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\nlohmann_json-src\include;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\fmt-src\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /utf-8 /await</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <ExceptionHandling>Sync</ExceptionHandling>
      <InlineFunctionExpansion>OnlyExplicitInline</InlineFunctionExpansion>
      <LanguageStandard>stdcpp20</LanguageStandard>
      <Optimization>MaxSpeed</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <TreatWarningAsError>true</TreatWarningAsError>
      <UseFullPaths>false</UseFullPaths>
      <WarningLevel>Level4</WarningLevel>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;SPDLOG_COMPILED_LIB;CMAKE_INTDIR="MinSizeRel"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <DebugInformationFormat>
      </DebugInformationFormat>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;SPDLOG_COMPILED_LIB;CMAKE_INTDIR=\"MinSizeRel\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>E:\lab\RoboQuant\src\Experiments\DataHub_Modern\include;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\spdlog-src\include;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\nlohmann_json-src\include;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\fmt-src\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>E:\lab\RoboQuant\src\Experiments\DataHub_Modern\include;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\spdlog-src\include;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\nlohmann_json-src\include;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\fmt-src\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Lib>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
    </Lib>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>E:\lab\RoboQuant\src\Experiments\DataHub_Modern\include;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\spdlog-src\include;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\nlohmann_json-src\include;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\fmt-src\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /utf-8 /await</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <ExceptionHandling>Sync</ExceptionHandling>
      <InlineFunctionExpansion>OnlyExplicitInline</InlineFunctionExpansion>
      <LanguageStandard>stdcpp20</LanguageStandard>
      <Optimization>MaxSpeed</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <TreatWarningAsError>true</TreatWarningAsError>
      <UseFullPaths>false</UseFullPaths>
      <WarningLevel>Level4</WarningLevel>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;SPDLOG_COMPILED_LIB;CMAKE_INTDIR="RelWithDebInfo"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;SPDLOG_COMPILED_LIB;CMAKE_INTDIR=\"RelWithDebInfo\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>E:\lab\RoboQuant\src\Experiments\DataHub_Modern\include;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\spdlog-src\include;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\nlohmann_json-src\include;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\fmt-src\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>E:\lab\RoboQuant\src\Experiments\DataHub_Modern\include;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\spdlog-src\include;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\nlohmann_json-src\include;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\fmt-src\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Lib>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
    </Lib>
  </ItemDefinitionGroup>
  <ItemGroup>
    <CustomBuild Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\CMakeLists.txt">
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Building Custom Rule E:/lab/RoboQuant/src/Experiments/DataHub_Modern/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
"D:\Program Files\CMake\bin\cmake.exe" -SE:/lab/RoboQuant/src/Experiments/DataHub_Modern -BE:/lab/RoboQuant/src/Experiments/DataHub_Modern/build --check-stamp-file E:/lab/RoboQuant/src/Experiments/DataHub_Modern/build/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:\Program Files\CMake\share\cmake-3.25\Modules\BasicConfigVersion-AnyNewerVersion.cmake.in;D:\Program Files\CMake\share\cmake-3.25\Modules\CMakeCXXInformation.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\CMakeCommonLanguageInclude.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\CMakeGenericSystem.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\CMakeInitializeConfigs.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\CMakeLanguageInformation.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\CMakePackageConfigHelpers.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\CMakeRCInformation.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\CMakeSystemSpecificInformation.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\CMakeSystemSpecificInitialize.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\CPack.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\CPackComponent.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\CheckCXXSourceCompiles.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\CheckIncludeFileCXX.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\CheckLibraryExists.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\Compiler\CMakeCommonCompilerMacros.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\Compiler\MSVC-CXX.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\Compiler\MSVC.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\FetchContent.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\FetchContent\CMakeLists.cmake.in;D:\Program Files\CMake\share\cmake-3.25\Modules\FindDoxygen.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\FindGit.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\FindPackageHandleStandardArgs.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\FindPackageMessage.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\FindThreads.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\Internal\CheckSourceCompiles.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\Platform\Windows-MSVC-CXX.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\Platform\Windows-MSVC.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\Platform\Windows.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\Platform\WindowsPaths.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\WriteBasicConfigVersionFile.cmake;D:\Program Files\CMake\share\cmake-3.25\Templates\CPackConfig.cmake.in;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\CMakeFiles\3.25.0-rc2\CMakeCXXCompiler.cmake;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\CMakeFiles\3.25.0-rc2\CMakeRCCompiler.cmake;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\CMakeFiles\3.25.0-rc2\CMakeSystem.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Building Custom Rule E:/lab/RoboQuant/src/Experiments/DataHub_Modern/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">setlocal
"D:\Program Files\CMake\bin\cmake.exe" -SE:/lab/RoboQuant/src/Experiments/DataHub_Modern -BE:/lab/RoboQuant/src/Experiments/DataHub_Modern/build --check-stamp-file E:/lab/RoboQuant/src/Experiments/DataHub_Modern/build/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:\Program Files\CMake\share\cmake-3.25\Modules\BasicConfigVersion-AnyNewerVersion.cmake.in;D:\Program Files\CMake\share\cmake-3.25\Modules\CMakeCXXInformation.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\CMakeCommonLanguageInclude.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\CMakeGenericSystem.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\CMakeInitializeConfigs.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\CMakeLanguageInformation.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\CMakePackageConfigHelpers.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\CMakeRCInformation.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\CMakeSystemSpecificInformation.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\CMakeSystemSpecificInitialize.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\CPack.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\CPackComponent.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\CheckCXXSourceCompiles.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\CheckIncludeFileCXX.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\CheckLibraryExists.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\Compiler\CMakeCommonCompilerMacros.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\Compiler\MSVC-CXX.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\Compiler\MSVC.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\FetchContent.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\FetchContent\CMakeLists.cmake.in;D:\Program Files\CMake\share\cmake-3.25\Modules\FindDoxygen.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\FindGit.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\FindPackageHandleStandardArgs.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\FindPackageMessage.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\FindThreads.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\Internal\CheckSourceCompiles.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\Platform\Windows-MSVC-CXX.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\Platform\Windows-MSVC.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\Platform\Windows.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\Platform\WindowsPaths.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\WriteBasicConfigVersionFile.cmake;D:\Program Files\CMake\share\cmake-3.25\Templates\CPackConfig.cmake.in;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\CMakeFiles\3.25.0-rc2\CMakeCXXCompiler.cmake;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\CMakeFiles\3.25.0-rc2\CMakeRCCompiler.cmake;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\CMakeFiles\3.25.0-rc2\CMakeSystem.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">Building Custom Rule E:/lab/RoboQuant/src/Experiments/DataHub_Modern/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">setlocal
"D:\Program Files\CMake\bin\cmake.exe" -SE:/lab/RoboQuant/src/Experiments/DataHub_Modern -BE:/lab/RoboQuant/src/Experiments/DataHub_Modern/build --check-stamp-file E:/lab/RoboQuant/src/Experiments/DataHub_Modern/build/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:\Program Files\CMake\share\cmake-3.25\Modules\BasicConfigVersion-AnyNewerVersion.cmake.in;D:\Program Files\CMake\share\cmake-3.25\Modules\CMakeCXXInformation.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\CMakeCommonLanguageInclude.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\CMakeGenericSystem.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\CMakeInitializeConfigs.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\CMakeLanguageInformation.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\CMakePackageConfigHelpers.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\CMakeRCInformation.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\CMakeSystemSpecificInformation.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\CMakeSystemSpecificInitialize.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\CPack.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\CPackComponent.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\CheckCXXSourceCompiles.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\CheckIncludeFileCXX.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\CheckLibraryExists.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\Compiler\CMakeCommonCompilerMacros.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\Compiler\MSVC-CXX.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\Compiler\MSVC.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\FetchContent.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\FetchContent\CMakeLists.cmake.in;D:\Program Files\CMake\share\cmake-3.25\Modules\FindDoxygen.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\FindGit.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\FindPackageHandleStandardArgs.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\FindPackageMessage.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\FindThreads.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\Internal\CheckSourceCompiles.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\Platform\Windows-MSVC-CXX.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\Platform\Windows-MSVC.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\Platform\Windows.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\Platform\WindowsPaths.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\WriteBasicConfigVersionFile.cmake;D:\Program Files\CMake\share\cmake-3.25\Templates\CPackConfig.cmake.in;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\CMakeFiles\3.25.0-rc2\CMakeCXXCompiler.cmake;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\CMakeFiles\3.25.0-rc2\CMakeRCCompiler.cmake;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\CMakeFiles\3.25.0-rc2\CMakeSystem.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">Building Custom Rule E:/lab/RoboQuant/src/Experiments/DataHub_Modern/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">setlocal
"D:\Program Files\CMake\bin\cmake.exe" -SE:/lab/RoboQuant/src/Experiments/DataHub_Modern -BE:/lab/RoboQuant/src/Experiments/DataHub_Modern/build --check-stamp-file E:/lab/RoboQuant/src/Experiments/DataHub_Modern/build/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:\Program Files\CMake\share\cmake-3.25\Modules\BasicConfigVersion-AnyNewerVersion.cmake.in;D:\Program Files\CMake\share\cmake-3.25\Modules\CMakeCXXInformation.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\CMakeCommonLanguageInclude.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\CMakeGenericSystem.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\CMakeInitializeConfigs.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\CMakeLanguageInformation.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\CMakePackageConfigHelpers.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\CMakeRCInformation.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\CMakeSystemSpecificInformation.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\CMakeSystemSpecificInitialize.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\CPack.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\CPackComponent.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\CheckCXXSourceCompiles.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\CheckIncludeFileCXX.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\CheckLibraryExists.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\Compiler\CMakeCommonCompilerMacros.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\Compiler\MSVC-CXX.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\Compiler\MSVC.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\FetchContent.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\FetchContent\CMakeLists.cmake.in;D:\Program Files\CMake\share\cmake-3.25\Modules\FindDoxygen.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\FindGit.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\FindPackageHandleStandardArgs.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\FindPackageMessage.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\FindThreads.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\Internal\CheckSourceCompiles.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\Platform\Windows-MSVC-CXX.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\Platform\Windows-MSVC.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\Platform\Windows.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\Platform\WindowsPaths.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\WriteBasicConfigVersionFile.cmake;D:\Program Files\CMake\share\cmake-3.25\Templates\CPackConfig.cmake.in;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\CMakeFiles\3.25.0-rc2\CMakeCXXCompiler.cmake;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\CMakeFiles\3.25.0-rc2\CMakeRCCompiler.cmake;E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\CMakeFiles\3.25.0-rc2\CMakeSystem.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\src\services\QuoteService.cpp" />
    <ClCompile Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\src\services\HistoryService.cpp" />
    <ClCompile Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\src\services\SecurityService.cpp" />
    <ClCompile Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\src\services\IndicatorService.cpp" />
    <ClCompile Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\src\services\EventBus.cpp" />
    <ClCompile Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\src\services\ServiceFactory.cpp" />
    <ClInclude Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\include\services\IDataService.h" />
    <ClInclude Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\include\services\QuoteService.h" />
    <ClInclude Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\include\services\HistoryService.h" />
    <ClInclude Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\include\services\SecurityService.h" />
    <ClInclude Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\include\services\IndicatorService.h" />
    <ClInclude Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\include\services\EventBus.h" />
    <ClInclude Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\include\services\ServiceFactory.h" />
    <Natvis Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\nlohmann_json-src\nlohmann_json.natvis">
    </Natvis>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\ZERO_CHECK.vcxproj">
      <Project>{E845B234-6F99-32FB-9478-5DF9566AB4BB}</Project>
      <Name>ZERO_CHECK</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
    <ProjectReference Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\datahub_core.vcxproj">
      <Project>{BBD6BED9-52DD-3B42-A3C8-1B2962C824CD}</Project>
      <Name>datahub_core</Name>
    </ProjectReference>
    <ProjectReference Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\datahub_data.vcxproj">
      <Project>{9F8C1425-A454-3C64-BD6B-B06574E87961}</Project>
      <Name>datahub_data</Name>
    </ProjectReference>
    <ProjectReference Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\fmt-build\fmt.vcxproj">
      <Project>{E14E2F0A-5EE3-3D20-8A77-533866B525A6}</Project>
      <Name>fmt</Name>
    </ProjectReference>
    <ProjectReference Include="E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\spdlog-build\spdlog.vcxproj">
      <Project>{BD106DC0-635E-3CCE-8587-9070DE8C267B}</Project>
      <Name>spdlog</Name>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>
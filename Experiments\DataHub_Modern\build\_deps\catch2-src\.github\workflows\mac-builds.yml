name: <PERSON> builds

on: [push, pull_request]

jobs:
  build:
    # macos-12 updated to a toolchain that crashes when linking the
    # test binary. This seems to be a known bug in that version,
    # and will eventually get fixed in an update. After that, we can go
    # back to newer macos images.
    runs-on: macos-11
    strategy:
      matrix:
        cxx:
          - g++-11
          - clang++
        build_type: [Debug, Release]
        std: [14, 17]
        include:
          - build_type: Debug
            examples: ON
            extra_tests: ON

    steps:
    - uses: actions/checkout@v2

    - name: Configure build
      working-directory: ${{runner.workspace}}
      env:
        CXX: ${{matrix.cxx}}
        CXXFLAGS: ${{matrix.cxxflags}}
      # Note: $GITHUB_WORKSPACE is distinct from ${{runner.workspace}}.
      #       This is important
      run: |
        cmake -Bbuild -H$GITHUB_WORKSPACE \
              -DCMAKE_BUILD_TYPE=${{matrix.build_type}} \
              -DCMAKE_CXX_STANDARD=${{matrix.std}} \
              -DCMAKE_CXX_STANDARD_REQUIRED=ON \
              -DCATCH_DEVELOPMENT_BUILD=ON \
              -DCATCH_BUILD_EXAMPLES=${{matrix.examples}} \
              -DCATCH_BUILD_EXTRA_TESTS=${{matrix.examples}}

    - name: Build tests + lib
      working-directory: ${{runner.workspace}}/build
      run: make -j 2

    - name: Run tests
      env:
          CTEST_OUTPUT_ON_FAILURE: 1
      working-directory: ${{runner.workspace}}/build
      # Hardcode 2 cores we know are there
      run: ctest -C ${{matrix.build_type}} -j 2

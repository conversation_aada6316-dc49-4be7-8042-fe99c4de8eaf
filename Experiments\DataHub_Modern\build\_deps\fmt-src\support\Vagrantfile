# -*- mode: ruby -*-
# vi: set ft=ruby :

# A vagrant config for testing against gcc-4.8.
Vagrant.configure("2") do |config|
  config.vm.box = "ubuntu/xenial64"
  config.disksize.size = '15GB'

  config.vm.provider "virtualbox" do |vb|
    vb.memory = "4096"
  end

  config.vm.provision "shell", inline: <<-SHELL
    apt-get update
    apt-get install -y g++ make wget git
    wget -q https://github.com/Kitware/CMake/releases/download/v3.26.0/cmake-3.26.0-Linux-x86_64.tar.gz
    tar xzf cmake-3.26.0-Linux-x86_64.tar.gz
    ln -s `pwd`/cmake-3.26.0-Linux-x86_64/bin/cmake /usr/local/bin
  SHELL
end

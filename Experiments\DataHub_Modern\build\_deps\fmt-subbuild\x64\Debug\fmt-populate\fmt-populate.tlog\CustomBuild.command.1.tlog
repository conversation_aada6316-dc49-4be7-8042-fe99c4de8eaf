^E:\LAB\ROBOQUANT\SRC\EXPERIMENTS\DATAHUB_MODERN\BUILD\_DEPS\FMT-SUBBUILD\CMAKEFILES\8D56DA1D1821C4688C2B3DC51B5A25F4\FMT-POPULATE-MKDIR.RULE
setlocal
"D:\Program Files\CMake\bin\cmake.exe" -Dcfgdir=/Debug -P E:/lab/RoboQuant/src/Experiments/DataHub_Modern/build/_deps/fmt-subbuild/fmt-populate-prefix/tmp/fmt-populate-mkdirs.cmake
if %errorlevel% neq 0 goto :cmEnd
"D:\Program Files\CMake\bin\cmake.exe" -E touch E:/lab/RoboQuant/src/Experiments/DataHub_Modern/build/_deps/fmt-subbuild/fmt-populate-prefix/src/fmt-populate-stamp/Debug/fmt-populate-mkdir
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^E:\LAB\ROBOQUANT\SRC\EXPERIMENTS\DATAHUB_MODERN\BUILD\_DEPS\FMT-SUBBUILD\CMAKEFILES\8D56DA1D1821C4688C2B3DC51B5A25F4\FMT-POPULATE-DOWNLOAD.RULE
setlocal
cd E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps
if %errorlevel% neq 0 goto :cmEnd
E:
if %errorlevel% neq 0 goto :cmEnd
"D:\Program Files\CMake\bin\cmake.exe" -P E:/lab/RoboQuant/src/Experiments/DataHub_Modern/build/_deps/fmt-subbuild/fmt-populate-prefix/tmp/fmt-populate-gitclone.cmake
if %errorlevel% neq 0 goto :cmEnd
"D:\Program Files\CMake\bin\cmake.exe" -E touch E:/lab/RoboQuant/src/Experiments/DataHub_Modern/build/_deps/fmt-subbuild/fmt-populate-prefix/src/fmt-populate-stamp/Debug/fmt-populate-download
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^E:\LAB\ROBOQUANT\SRC\EXPERIMENTS\DATAHUB_MODERN\BUILD\_DEPS\FMT-SUBBUILD\CMAKEFILES\8D56DA1D1821C4688C2B3DC51B5A25F4\FMT-POPULATE-UPDATE.RULE
setlocal
cd E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\fmt-src
if %errorlevel% neq 0 goto :cmEnd
E:
if %errorlevel% neq 0 goto :cmEnd
"D:\Program Files\CMake\bin\cmake.exe" -P E:/lab/RoboQuant/src/Experiments/DataHub_Modern/build/_deps/fmt-subbuild/fmt-populate-prefix/tmp/fmt-populate-gitupdate.cmake
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^E:\LAB\ROBOQUANT\SRC\EXPERIMENTS\DATAHUB_MODERN\BUILD\_DEPS\FMT-SUBBUILD\CMAKEFILES\8D56DA1D1821C4688C2B3DC51B5A25F4\FMT-POPULATE-PATCH.RULE
setlocal
"D:\Program Files\CMake\bin\cmake.exe" -E echo_append
if %errorlevel% neq 0 goto :cmEnd
"D:\Program Files\CMake\bin\cmake.exe" -E touch E:/lab/RoboQuant/src/Experiments/DataHub_Modern/build/_deps/fmt-subbuild/fmt-populate-prefix/src/fmt-populate-stamp/Debug/fmt-populate-patch
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^E:\LAB\ROBOQUANT\SRC\EXPERIMENTS\DATAHUB_MODERN\BUILD\_DEPS\FMT-SUBBUILD\CMAKEFILES\8D56DA1D1821C4688C2B3DC51B5A25F4\FMT-POPULATE-CONFIGURE.RULE
setlocal
cd E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\fmt-build
if %errorlevel% neq 0 goto :cmEnd
E:
if %errorlevel% neq 0 goto :cmEnd
"D:\Program Files\CMake\bin\cmake.exe" -E echo_append
if %errorlevel% neq 0 goto :cmEnd
"D:\Program Files\CMake\bin\cmake.exe" -E touch E:/lab/RoboQuant/src/Experiments/DataHub_Modern/build/_deps/fmt-subbuild/fmt-populate-prefix/src/fmt-populate-stamp/Debug/fmt-populate-configure
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^E:\LAB\ROBOQUANT\SRC\EXPERIMENTS\DATAHUB_MODERN\BUILD\_DEPS\FMT-SUBBUILD\CMAKEFILES\8D56DA1D1821C4688C2B3DC51B5A25F4\FMT-POPULATE-BUILD.RULE
setlocal
cd E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\fmt-build
if %errorlevel% neq 0 goto :cmEnd
E:
if %errorlevel% neq 0 goto :cmEnd
"D:\Program Files\CMake\bin\cmake.exe" -E echo_append
if %errorlevel% neq 0 goto :cmEnd
"D:\Program Files\CMake\bin\cmake.exe" -E touch E:/lab/RoboQuant/src/Experiments/DataHub_Modern/build/_deps/fmt-subbuild/fmt-populate-prefix/src/fmt-populate-stamp/Debug/fmt-populate-build
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^E:\LAB\ROBOQUANT\SRC\EXPERIMENTS\DATAHUB_MODERN\BUILD\_DEPS\FMT-SUBBUILD\CMAKEFILES\8D56DA1D1821C4688C2B3DC51B5A25F4\FMT-POPULATE-INSTALL.RULE
setlocal
cd E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\fmt-build
if %errorlevel% neq 0 goto :cmEnd
E:
if %errorlevel% neq 0 goto :cmEnd
"D:\Program Files\CMake\bin\cmake.exe" -E echo_append
if %errorlevel% neq 0 goto :cmEnd
"D:\Program Files\CMake\bin\cmake.exe" -E touch E:/lab/RoboQuant/src/Experiments/DataHub_Modern/build/_deps/fmt-subbuild/fmt-populate-prefix/src/fmt-populate-stamp/Debug/fmt-populate-install
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^E:\LAB\ROBOQUANT\SRC\EXPERIMENTS\DATAHUB_MODERN\BUILD\_DEPS\FMT-SUBBUILD\CMAKEFILES\8D56DA1D1821C4688C2B3DC51B5A25F4\FMT-POPULATE-TEST.RULE
setlocal
cd E:\lab\RoboQuant\src\Experiments\DataHub_Modern\build\_deps\fmt-build
if %errorlevel% neq 0 goto :cmEnd
E:
if %errorlevel% neq 0 goto :cmEnd
"D:\Program Files\CMake\bin\cmake.exe" -E echo_append
if %errorlevel% neq 0 goto :cmEnd
"D:\Program Files\CMake\bin\cmake.exe" -E touch E:/lab/RoboQuant/src/Experiments/DataHub_Modern/build/_deps/fmt-subbuild/fmt-populate-prefix/src/fmt-populate-stamp/Debug/fmt-populate-test
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^E:\LAB\ROBOQUANT\SRC\EXPERIMENTS\DATAHUB_MODERN\BUILD\_DEPS\FMT-SUBBUILD\CMAKEFILES\36D01E6780F36A77634B80FED8115060\FMT-POPULATE-COMPLETE.RULE
setlocal
"D:\Program Files\CMake\bin\cmake.exe" -E make_directory E:/lab/RoboQuant/src/Experiments/DataHub_Modern/build/_deps/fmt-subbuild/CMakeFiles/Debug
if %errorlevel% neq 0 goto :cmEnd
"D:\Program Files\CMake\bin\cmake.exe" -E touch E:/lab/RoboQuant/src/Experiments/DataHub_Modern/build/_deps/fmt-subbuild/CMakeFiles/Debug/fmt-populate-complete
if %errorlevel% neq 0 goto :cmEnd
"D:\Program Files\CMake\bin\cmake.exe" -E touch E:/lab/RoboQuant/src/Experiments/DataHub_Modern/build/_deps/fmt-subbuild/fmt-populate-prefix/src/fmt-populate-stamp/Debug/fmt-populate-done
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^E:\LAB\ROBOQUANT\SRC\EXPERIMENTS\DATAHUB_MODERN\BUILD\_DEPS\FMT-SUBBUILD\CMAKEFILES\1B46B95CC0E093829732605E64A98265\FMT-POPULATE.RULE
setlocal
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^E:\LAB\ROBOQUANT\SRC\EXPERIMENTS\DATAHUB_MODERN\BUILD\_DEPS\FMT-SUBBUILD\CMAKELISTS.TXT
setlocal
"D:\Program Files\CMake\bin\cmake.exe" -SE:/lab/RoboQuant/src/Experiments/DataHub_Modern/build/_deps/fmt-subbuild -BE:/lab/RoboQuant/src/Experiments/DataHub_Modern/build/_deps/fmt-subbuild --check-stamp-file E:/lab/RoboQuant/src/Experiments/DataHub_Modern/build/_deps/fmt-subbuild/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd

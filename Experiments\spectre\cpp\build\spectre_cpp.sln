﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ALL_BUILD", "ALL_BUILD.vcxproj", "{81536017-8C49-3C08-A292-69730B6BCA7E}"
	ProjectSection(ProjectDependencies) = postProject
		{FF0C6A13-1074-3F80-B450-531EC93F6D6D} = {FF0C6A13-1074-3F80-B450-531EC93F6D6D}
		{12B55FBB-B7FB-34E8-9F5F-6202944A5E18} = {12B55FBB-B7FB-34E8-9F5F-6202944A5E18}
		{9F59193F-CA70-31EC-95D0-C8717B9854AC} = {9F59193F-CA70-31EC-95D0-C8717B9854<PERSON>}
		{6BBFC65B-65AE-34A2-8725-672187ACEC31} = {6BBFC65B-65AE-34A2-8725-672187ACEC31}
		{81B472DE-6B1C-3B12-92CE-0F5FCF8F4117} = {81B472DE-6B1C-3B12-92CE-0F5FCF8F4117}
		{09C13C03-6E44-3763-B15C-621BF93F394A} = {09C13C03-6E44-3763-B15C-621BF93F394A}
		{5A4E0A67-6962-30CA-822A-64A51D67AD3E} = {5A4E0A67-6962-30CA-822A-64A51D67AD3E}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ZERO_CHECK", "ZERO_CHECK.vcxproj", "{FF0C6A13-1074-3F80-B450-531EC93F6D6D}"
	ProjectSection(ProjectDependencies) = postProject
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "spectre_main", "spectre_main.vcxproj", "{12B55FBB-B7FB-34E8-9F5F-6202944A5E18}"
	ProjectSection(ProjectDependencies) = postProject
		{FF0C6A13-1074-3F80-B450-531EC93F6D6D} = {FF0C6A13-1074-3F80-B450-531EC93F6D6D}
		{9F59193F-CA70-31EC-95D0-C8717B9854AC} = {9F59193F-CA70-31EC-95D0-C8717B9854AC}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "spectre_trading", "spectre_trading.vcxproj", "{9F59193F-CA70-31EC-95D0-C8717B9854AC}"
	ProjectSection(ProjectDependencies) = postProject
		{FF0C6A13-1074-3F80-B450-531EC93F6D6D} = {FF0C6A13-1074-3F80-B450-531EC93F6D6D}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "test_simple", "test_simple.vcxproj", "{6BBFC65B-65AE-34A2-8725-672187ACEC31}"
	ProjectSection(ProjectDependencies) = postProject
		{FF0C6A13-1074-3F80-B450-531EC93F6D6D} = {FF0C6A13-1074-3F80-B450-531EC93F6D6D}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "test_trading", "test_trading.vcxproj", "{81B472DE-6B1C-3B12-92CE-0F5FCF8F4117}"
	ProjectSection(ProjectDependencies) = postProject
		{FF0C6A13-1074-3F80-B450-531EC93F6D6D} = {FF0C6A13-1074-3F80-B450-531EC93F6D6D}
		{9F59193F-CA70-31EC-95D0-C8717B9854AC} = {9F59193F-CA70-31EC-95D0-C8717B9854AC}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "test_trading_extended", "test_trading_extended.vcxproj", "{09C13C03-6E44-3763-B15C-621BF93F394A}"
	ProjectSection(ProjectDependencies) = postProject
		{FF0C6A13-1074-3F80-B450-531EC93F6D6D} = {FF0C6A13-1074-3F80-B450-531EC93F6D6D}
		{9F59193F-CA70-31EC-95D0-C8717B9854AC} = {9F59193F-CA70-31EC-95D0-C8717B9854AC}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "trading_example", "trading_example.vcxproj", "{5A4E0A67-6962-30CA-822A-64A51D67AD3E}"
	ProjectSection(ProjectDependencies) = postProject
		{FF0C6A13-1074-3F80-B450-531EC93F6D6D} = {FF0C6A13-1074-3F80-B450-531EC93F6D6D}
		{9F59193F-CA70-31EC-95D0-C8717B9854AC} = {9F59193F-CA70-31EC-95D0-C8717B9854AC}
	EndProjectSection
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|x64 = Debug|x64
		Release|x64 = Release|x64
		MinSizeRel|x64 = MinSizeRel|x64
		RelWithDebInfo|x64 = RelWithDebInfo|x64
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{81536017-8C49-3C08-A292-69730B6BCA7E}.Debug|x64.ActiveCfg = Debug|x64
		{81536017-8C49-3C08-A292-69730B6BCA7E}.Release|x64.ActiveCfg = Release|x64
		{81536017-8C49-3C08-A292-69730B6BCA7E}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{81536017-8C49-3C08-A292-69730B6BCA7E}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{FF0C6A13-1074-3F80-B450-531EC93F6D6D}.Debug|x64.ActiveCfg = Debug|x64
		{FF0C6A13-1074-3F80-B450-531EC93F6D6D}.Debug|x64.Build.0 = Debug|x64
		{FF0C6A13-1074-3F80-B450-531EC93F6D6D}.Release|x64.ActiveCfg = Release|x64
		{FF0C6A13-1074-3F80-B450-531EC93F6D6D}.Release|x64.Build.0 = Release|x64
		{FF0C6A13-1074-3F80-B450-531EC93F6D6D}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{FF0C6A13-1074-3F80-B450-531EC93F6D6D}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{FF0C6A13-1074-3F80-B450-531EC93F6D6D}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{FF0C6A13-1074-3F80-B450-531EC93F6D6D}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{12B55FBB-B7FB-34E8-9F5F-6202944A5E18}.Debug|x64.ActiveCfg = Debug|x64
		{12B55FBB-B7FB-34E8-9F5F-6202944A5E18}.Debug|x64.Build.0 = Debug|x64
		{12B55FBB-B7FB-34E8-9F5F-6202944A5E18}.Release|x64.ActiveCfg = Release|x64
		{12B55FBB-B7FB-34E8-9F5F-6202944A5E18}.Release|x64.Build.0 = Release|x64
		{12B55FBB-B7FB-34E8-9F5F-6202944A5E18}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{12B55FBB-B7FB-34E8-9F5F-6202944A5E18}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{12B55FBB-B7FB-34E8-9F5F-6202944A5E18}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{12B55FBB-B7FB-34E8-9F5F-6202944A5E18}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{9F59193F-CA70-31EC-95D0-C8717B9854AC}.Debug|x64.ActiveCfg = Debug|x64
		{9F59193F-CA70-31EC-95D0-C8717B9854AC}.Debug|x64.Build.0 = Debug|x64
		{9F59193F-CA70-31EC-95D0-C8717B9854AC}.Release|x64.ActiveCfg = Release|x64
		{9F59193F-CA70-31EC-95D0-C8717B9854AC}.Release|x64.Build.0 = Release|x64
		{9F59193F-CA70-31EC-95D0-C8717B9854AC}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{9F59193F-CA70-31EC-95D0-C8717B9854AC}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{9F59193F-CA70-31EC-95D0-C8717B9854AC}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{9F59193F-CA70-31EC-95D0-C8717B9854AC}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{6BBFC65B-65AE-34A2-8725-672187ACEC31}.Debug|x64.ActiveCfg = Debug|x64
		{6BBFC65B-65AE-34A2-8725-672187ACEC31}.Debug|x64.Build.0 = Debug|x64
		{6BBFC65B-65AE-34A2-8725-672187ACEC31}.Release|x64.ActiveCfg = Release|x64
		{6BBFC65B-65AE-34A2-8725-672187ACEC31}.Release|x64.Build.0 = Release|x64
		{6BBFC65B-65AE-34A2-8725-672187ACEC31}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{6BBFC65B-65AE-34A2-8725-672187ACEC31}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{6BBFC65B-65AE-34A2-8725-672187ACEC31}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{6BBFC65B-65AE-34A2-8725-672187ACEC31}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{81B472DE-6B1C-3B12-92CE-0F5FCF8F4117}.Debug|x64.ActiveCfg = Debug|x64
		{81B472DE-6B1C-3B12-92CE-0F5FCF8F4117}.Debug|x64.Build.0 = Debug|x64
		{81B472DE-6B1C-3B12-92CE-0F5FCF8F4117}.Release|x64.ActiveCfg = Release|x64
		{81B472DE-6B1C-3B12-92CE-0F5FCF8F4117}.Release|x64.Build.0 = Release|x64
		{81B472DE-6B1C-3B12-92CE-0F5FCF8F4117}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{81B472DE-6B1C-3B12-92CE-0F5FCF8F4117}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{81B472DE-6B1C-3B12-92CE-0F5FCF8F4117}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{81B472DE-6B1C-3B12-92CE-0F5FCF8F4117}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{09C13C03-6E44-3763-B15C-621BF93F394A}.Debug|x64.ActiveCfg = Debug|x64
		{09C13C03-6E44-3763-B15C-621BF93F394A}.Debug|x64.Build.0 = Debug|x64
		{09C13C03-6E44-3763-B15C-621BF93F394A}.Release|x64.ActiveCfg = Release|x64
		{09C13C03-6E44-3763-B15C-621BF93F394A}.Release|x64.Build.0 = Release|x64
		{09C13C03-6E44-3763-B15C-621BF93F394A}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{09C13C03-6E44-3763-B15C-621BF93F394A}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{09C13C03-6E44-3763-B15C-621BF93F394A}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{09C13C03-6E44-3763-B15C-621BF93F394A}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{5A4E0A67-6962-30CA-822A-64A51D67AD3E}.Debug|x64.ActiveCfg = Debug|x64
		{5A4E0A67-6962-30CA-822A-64A51D67AD3E}.Debug|x64.Build.0 = Debug|x64
		{5A4E0A67-6962-30CA-822A-64A51D67AD3E}.Release|x64.ActiveCfg = Release|x64
		{5A4E0A67-6962-30CA-822A-64A51D67AD3E}.Release|x64.Build.0 = Release|x64
		{5A4E0A67-6962-30CA-822A-64A51D67AD3E}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{5A4E0A67-6962-30CA-822A-64A51D67AD3E}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{5A4E0A67-6962-30CA-822A-64A51D67AD3E}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{5A4E0A67-6962-30CA-822A-64A51D67AD3E}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {9FB6AAFD-6DA1-33D8-AC8D-F86399BB897A}
	EndGlobalSection
	GlobalSection(ExtensibilityAddIns) = postSolution
	EndGlobalSection
EndGlobal

version: "{build}-{branch}"

# If we ever get a backlog larger than clone_depth, builds will fail
# spuriously. I do not think we will ever get 20 deep commits deep though.
clone_depth: 20

# We want to build everything, except for branches that are explicitly
# for messing around with travis.
branches:
  except:
    - /dev-travis.+/


# We need a more up to date pip because Python 2.7 is EOL soon
init:
  - set PATH=C:\Python35;C:\Python35\Scripts;%PATH%


install:
  - ps: if (($env:CONFIGURATION) -eq "Debug" -And ($env:coverage) -eq "1" ) { pip --disable-pip-version-check install codecov }
  # This removes our changes to PATH. Keep this step last!
  - ps: if (($env:CONFIGURATION) -eq "Debug" -And ($env:coverage) -eq "1" ) { .\tools\misc\installOpenCppCoverage.ps1 }


before_build:
  # We need to modify PATH again, because it was reset since the "init" step
  - set PATH=C:\Python35;C:\Python35\Scripts;%PATH%
  - set CXXFLAGS=%additional_flags%
  # If we are building examples/extra-tests, we need to regenerate the amalgamated files
  - cmd: if "%examples%"=="1" ( python .\tools\scripts\generateAmalgamatedFiles.py )
  # Indirection because appveyor doesn't handle multiline batch scripts properly
  # https://stackoverflow.com/questions/37627248/how-to-split-a-command-over-multiple-lines-in-appveyor-yml/37647169#37647169
  # https://help.appveyor.com/discussions/questions/3888-multi-line-cmd-or-powershell-warning-ignore
  - cmd: .\tools\misc\appveyorBuildConfigurationScript.bat


# build with MSBuild
build:
  project: Build\Catch2.sln             # path to Visual Studio solution or project
  parallel: true                        # enable MSBuild parallel builds
  verbosity: normal                     # MSBuild verbosity level {quiet|minimal|normal|detailed}

test_script:
  - set CTEST_OUTPUT_ON_FAILURE=1
  - cmd: .\tools\misc\appveyorTestRunScript.bat


# Sadly we cannot use the standard "dimensions" based approach towards
# specifying the different builds, as there is no way to add one-offs
# builds afterwards. This means that we will painfully specify each
# build explicitly.
environment:
  matrix:
    - FLAVOR: VS 2019 x64 Debug Coverage Examples
      APPVEYOR_BUILD_WORKER_IMAGE: Visual Studio 2019
      examples: 1
      coverage: 1
      platform: x64
      configuration: Debug

    - FLAVOR: VS 2019 x64 Debug WMain
      APPVEYOR_BUILD_WORKER_IMAGE: Visual Studio 2019
      wmain: 1
      additional_flags: "/D_UNICODE /DUNICODE"
      platform: x64
      configuration: Debug

    - FLAVOR: VS 2019 x64 Debug Latest Strict
      APPVEYOR_BUILD_WORKER_IMAGE: Visual Studio 2019
      additional_flags: "/permissive- /std:c++latest"
      platform: x64
      configuration: Debug

    - FLAVOR: VS 2017 x64 Debug
      APPVEYOR_BUILD_WORKER_IMAGE: Visual Studio 2017
      platform: x64
      configuration: Debug

    - FLAVOR: VS 2017 x64 Release Coverage
      APPVEYOR_BUILD_WORKER_IMAGE: Visual Studio 2017
      coverage: 1
      platform: x64
      configuration: Debug